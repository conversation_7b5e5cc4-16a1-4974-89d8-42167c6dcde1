{"name": "think-chat-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.3", "terser": "^5.31.0", "typescript": "^5.2.2", "vite": "^5.2.0"}}