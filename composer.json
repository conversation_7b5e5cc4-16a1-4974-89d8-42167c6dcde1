{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0|^4.0", "topthink/think-filesystem": "^2.0|^3.0", "topthink/think-ai": "dev-master"}, "require-dev": {"topthink/think-dumper": "^1.0", "topthink/think-trace": "^2.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "platform-check": false}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}