import{r as e,a as t,c as n,g as r,R as o}from"./vendor-D-XgqoRR.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var i={exports:{}},l={},s=e,a=Symbol.for("react.element"),c=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,f=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function p(e,t,n){var r,o={},i=null,l=null;for(r in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(l=t.ref),t)u.call(t,r)&&!d.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:i,ref:l,props:o,_owner:f.current}}l.Fragment=c,l.jsx=p,l.jsxs=p,i.exports=l;var h=i.exports,m={},g=t;m.createRoot=g.createRoot,m.hydrateRoot=g.hydrateRoot;const y=({onClearChat:e})=>h.jsx("header",{className:"bg-white border-b border-gray-200 px-4 py-3 shadow-sm",children:h.jsxs("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[h.jsxs("div",{className:"flex items-center space-x-3",children:[h.jsx("div",{className:"w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center",children:h.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),h.jsxs("div",{children:[h.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:"Think Chat"}),h.jsx("p",{className:"text-sm text-gray-500",children:"AI对话助手"})]})]}),h.jsx("div",{className:"flex items-center space-x-2",children:h.jsxs("button",{onClick:e,className:"header-button",title:"清空对话",children:[h.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),h.jsx("span",{children:"清空"})]})})]})});function x(){}function k(){}const v=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,b=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,w={};function S(e,t){return(w.jsx?b:v).test(e)}const C=/[ \t\n\f\r]/g;function E(e){return""===e.replace(C,"")}class I{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function T(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new I(n,r,t)}function A(e){return e.toLowerCase()}I.prototype.normal={},I.prototype.property={},I.prototype.space=void 0;class P{constructor(e,t){this.attribute=t,this.property=e}}P.prototype.attribute="",P.prototype.booleanish=!1,P.prototype.boolean=!1,P.prototype.commaOrSpaceSeparated=!1,P.prototype.commaSeparated=!1,P.prototype.defined=!1,P.prototype.mustUseProperty=!1,P.prototype.number=!1,P.prototype.overloadedBoolean=!1,P.prototype.property="",P.prototype.spaceSeparated=!1,P.prototype.space=void 0;let L=0;const D=R(),j=R(),O=R(),N=R(),M=R(),F=R(),z=R();function R(){return 2**++L}const _=Object.freeze(Object.defineProperty({__proto__:null,boolean:D,booleanish:j,commaOrSpaceSeparated:z,commaSeparated:F,number:N,overloadedBoolean:O,spaceSeparated:M},Symbol.toStringTag,{value:"Module"})),B=Object.keys(_);class H extends P{constructor(e,t,n,r){let o=-1;if(super(e,t),U(this,"space",r),"number"==typeof n)for(;++o<B.length;){const e=B[o];U(this,B[o],(n&_[e])===_[e])}}}function U(e,t,n){n&&(e[t]=n)}function V(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new H(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[A(r)]=r,n[A(i.attribute)]=r}return new I(t,n,e.space)}H.prototype.defined=!0;const q=V({properties:{ariaActiveDescendant:null,ariaAtomic:j,ariaAutoComplete:null,ariaBusy:j,ariaChecked:j,ariaColCount:N,ariaColIndex:N,ariaColSpan:N,ariaControls:M,ariaCurrent:null,ariaDescribedBy:M,ariaDetails:null,ariaDisabled:j,ariaDropEffect:M,ariaErrorMessage:null,ariaExpanded:j,ariaFlowTo:M,ariaGrabbed:j,ariaHasPopup:null,ariaHidden:j,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:M,ariaLevel:N,ariaLive:null,ariaModal:j,ariaMultiLine:j,ariaMultiSelectable:j,ariaOrientation:null,ariaOwns:M,ariaPlaceholder:null,ariaPosInSet:N,ariaPressed:j,ariaReadOnly:j,ariaRelevant:null,ariaRequired:j,ariaRoleDescription:M,ariaRowCount:N,ariaRowIndex:N,ariaRowSpan:N,ariaSelected:j,ariaSetSize:N,ariaSort:null,ariaValueMax:N,ariaValueMin:N,ariaValueNow:N,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function W(e,t){return t in e?e[t]:t}function $(e,t){return W(e,t.toLowerCase())}const Y=V({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:F,acceptCharset:M,accessKey:M,action:null,allow:null,allowFullScreen:D,allowPaymentRequest:D,allowUserMedia:D,alt:null,as:null,async:D,autoCapitalize:null,autoComplete:M,autoFocus:D,autoPlay:D,blocking:M,capture:null,charSet:null,checked:D,cite:null,className:M,cols:N,colSpan:null,content:null,contentEditable:j,controls:D,controlsList:M,coords:N|F,crossOrigin:null,data:null,dateTime:null,decoding:null,default:D,defer:D,dir:null,dirName:null,disabled:D,download:O,draggable:j,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:D,formTarget:null,headers:M,height:N,hidden:O,high:N,href:null,hrefLang:null,htmlFor:M,httpEquiv:M,id:null,imageSizes:null,imageSrcSet:null,inert:D,inputMode:null,integrity:null,is:null,isMap:D,itemId:null,itemProp:M,itemRef:M,itemScope:D,itemType:M,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:D,low:N,manifest:null,max:null,maxLength:N,media:null,method:null,min:null,minLength:N,multiple:D,muted:D,name:null,nonce:null,noModule:D,noValidate:D,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:D,optimum:N,pattern:null,ping:M,placeholder:null,playsInline:D,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:D,referrerPolicy:null,rel:M,required:D,reversed:D,rows:N,rowSpan:N,sandbox:M,scope:null,scoped:D,seamless:D,selected:D,shadowRootClonable:D,shadowRootDelegatesFocus:D,shadowRootMode:null,shape:null,size:N,sizes:null,slot:null,span:N,spellCheck:j,src:null,srcDoc:null,srcLang:null,srcSet:null,start:N,step:null,style:null,tabIndex:N,target:null,title:null,translate:null,type:null,typeMustMatch:D,useMap:null,value:j,width:N,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:M,axis:null,background:null,bgColor:null,border:N,borderColor:null,bottomMargin:N,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:D,declare:D,event:null,face:null,frame:null,frameBorder:null,hSpace:N,leftMargin:N,link:null,longDesc:null,lowSrc:null,marginHeight:N,marginWidth:N,noResize:D,noHref:D,noShade:D,noWrap:D,object:null,profile:null,prompt:null,rev:null,rightMargin:N,rules:null,scheme:null,scrolling:j,standby:null,summary:null,text:null,topMargin:N,valueType:null,version:null,vAlign:null,vLink:null,vSpace:N,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:D,disableRemotePlayback:D,prefix:null,property:null,results:N,security:null,unselectable:null},space:"html",transform:$}),K=V({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:z,accentHeight:N,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:N,amplitude:N,arabicForm:null,ascent:N,attributeName:null,attributeType:null,azimuth:N,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:N,by:null,calcMode:null,capHeight:N,className:M,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:N,diffuseConstant:N,direction:null,display:null,dur:null,divisor:N,dominantBaseline:null,download:D,dx:null,dy:null,edgeMode:null,editable:null,elevation:N,enableBackground:null,end:null,event:null,exponent:N,externalResourcesRequired:null,fill:null,fillOpacity:N,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:F,g2:F,glyphName:F,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:N,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:N,horizOriginX:N,horizOriginY:N,id:null,ideographic:N,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:N,k:N,k1:N,k2:N,k3:N,k4:N,kernelMatrix:z,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:N,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:N,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:N,overlineThickness:N,paintOrder:null,panose1:null,path:null,pathLength:N,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:M,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:N,pointsAtY:N,pointsAtZ:N,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:z,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:z,rev:z,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:z,requiredFeatures:z,requiredFonts:z,requiredFormats:z,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:N,specularExponent:N,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:N,strikethroughThickness:N,string:null,stroke:null,strokeDashArray:z,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:N,strokeOpacity:N,strokeWidth:null,style:null,surfaceScale:N,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:z,tabIndex:N,tableValues:null,target:null,targetX:N,targetY:N,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:z,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:N,underlineThickness:N,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:N,values:null,vAlphabetic:N,vMathematical:N,vectorEffect:null,vHanging:N,vIdeographic:N,version:null,vertAdvY:N,vertOriginX:N,vertOriginY:N,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:N,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:W}),Q=V({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),J=V({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:$}),X=V({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),Z={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},G=/[A-Z]/g,ee=/-[a-z]/g,te=/^data[-\w.:]+$/i;function ne(e){return"-"+e.toLowerCase()}function re(e){return e.charAt(1).toUpperCase()}const oe=T([q,Y,Q,J,X],"html"),ie=T([q,K,Q,J,X],"svg");var le={},se=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,ae=/\n/g,ce=/^\s*/,ue=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,fe=/^:\s*/,de=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,pe=/^[;\s]*/,he=/^\s+|\s+$/g,me="";function ge(e){return e?e.replace(he,me):me}var ye=n&&n.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(le,"__esModule",{value:!0}),le.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,xe.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var xe=ye(function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function o(e){var t=e.match(ae);t&&(n+=t.length);var o=e.lastIndexOf("\n");r=~o?e.length-o:r+e.length}function i(){var e={line:n,column:r};return function(t){return t.position=new l(e),c(),t}}function l(e){this.start=e,this.end={line:n,column:r},this.source=t.source}function s(o){var i=new Error(t.source+":"+n+":"+r+": "+o);if(i.reason=o,i.filename=t.source,i.line=n,i.column=r,i.source=e,!t.silent)throw i}function a(t){var n=t.exec(e);if(n){var r=n[0];return o(r),e=e.slice(r.length),n}}function c(){a(ce)}function u(e){var t;for(e=e||[];t=f();)!1!==t&&e.push(t);return e}function f(){var t=i();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;me!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,me===e.charAt(n-1))return s("End of comment missing");var l=e.slice(2,n-2);return r+=2,o(l),e=e.slice(n),r+=2,t({type:"comment",comment:l})}}function d(){var e=i(),t=a(ue);if(t){if(f(),!a(fe))return s("property missing ':'");var n=a(de),r=e({type:"declaration",property:ge(t[0].replace(se,me)),value:n?ge(n[0].replace(se,me)):me});return a(pe),r}}return l.prototype.content=e,c(),function(){var e,t=[];for(u(t);e=d();)!1!==e&&(t.push(e),u(t));return t}()});var ke={};Object.defineProperty(ke,"__esModule",{value:!0}),ke.camelCase=void 0;var ve=/^--[a-zA-Z0-9_-]+$/,be=/-([a-z])/g,we=/^[^-]+$/,Se=/^-(webkit|moz|ms|o|khtml)-/,Ce=/^-(ms)-/,Ee=function(e,t){return t.toUpperCase()},Ie=function(e,t){return"".concat(t,"-")};ke.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||we.test(e)||ve.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(Ce,Ie):e.replace(Se,Ie)).replace(be,Ee))};var Te=(n&&n.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(le),Ae=ke;function Pe(e,t){var n={};return e&&"string"==typeof e?((0,Te.default)(e,function(e,r){e&&r&&(n[(0,Ae.camelCase)(e,t)]=r)}),n):n}Pe.default=Pe;const Le=r(Pe),De=Oe("end"),je=Oe("start");function Oe(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Ne(e){return e&&"object"==typeof e?"position"in e||"type"in e?Fe(e.position):"start"in e||"end"in e?Fe(e):"line"in e||"column"in e?Me(e):"":""}function Me(e){return ze(e&&e.line)+":"+ze(e&&e.column)}function Fe(e){return Me(e&&e.start)+"-"+Me(e&&e.end)}function ze(e){return e&&"number"==typeof e?e:1}class Re extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file="",this.message=r,this.line=l?l.line:void 0,this.name=Ne(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}Re.prototype.file="",Re.prototype.name="",Re.prototype.reason="",Re.prototype.message="",Re.prototype.stack="",Re.prototype.column=void 0,Re.prototype.line=void 0,Re.prototype.ancestors=void 0,Re.prototype.cause=void 0,Re.prototype.fatal=void 0,Re.prototype.place=void 0,Re.prototype.ruleId=void 0,Re.prototype.source=void 0;const _e={}.hasOwnProperty,Be=new Map,He=/[A-Z]/g,Ue=new Set(["table","tbody","thead","tfoot","tr"]),Ve=new Set(["td","th"]),qe="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function We(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,t){return n;function n(n,r,o,i){const l=Array.isArray(o.children),s=je(n);return t(r,o,i,l,{columnNumber:s?s.column-1:void 0,fileName:e,lineNumber:s?s.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");r=function(e,t,n){return r;function r(e,r,o,i){const l=Array.isArray(o.children)?n:t;return i?l(r,o,i):l(r,o)}}(0,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?ie:oe,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},i=$e(o,e,void 0);return i&&"string"!=typeof i?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function $e(e,t,n){return"element"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(o=ie,e.schema=o);e.ancestors.push(t);const i=Xe(e,t.tagName,!1),l=function(e,t){const n={};let r,o;for(o in t.properties)if("children"!==o&&_e.call(t.properties,o)){const i=Je(e,o,t.properties[o]);if(i){const[o,l]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof l&&Ve.has(t.tagName)?r=l:n[o]=l}}if(r){(n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r}return n}(e,t);let s=Qe(e,t);Ue.has(t.tagName)&&(s=s.filter(function(e){return"string"!=typeof e||!("object"==typeof(t=e)?"text"===t.type&&E(t.value):E(t));var t}));return Ye(e,l,i,t),Ke(l,s),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}Ze(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.name&&"html"===r.space&&(o=ie,e.schema=o);e.ancestors.push(t);const i=null===t.name?e.Fragment:Xe(e,t.name,!0),l=function(e,t){const n={};for(const r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const t=r.data.estree.body[0];x(t.type);const o=t.expression;x(o.type);const i=o.properties[0];x(i.type),Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else Ze(e,t.position);else{const o=r.name;let i;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const t=r.value.data.estree.body[0];x(t.type),i=e.evaluater.evaluateExpression(t.expression)}else Ze(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),s=Qe(e,t);return Ye(e,l,i,t),Ke(l,s),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);Ze(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const r={};return Ke(r,Qe(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function Ye(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Ke(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function Qe(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:Be;for(;++r<t.children.length;){const i=t.children[r];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=o.get(e)||0;l=e+"-"+t,o.set(e,t+1)}}const s=$e(e,i,l);void 0!==s&&n.push(s)}return n}function Je(e,t,n){const r=function(e,t){const n=A(t);let r=t,o=P;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&te.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(ee,re);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!ee.test(e)){let n=e.replace(G,ne);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}o=H}return new o(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e){const t={};return(""===e[e.length-1]?[...e,""]:e).join((t.padRight?" ":"")+","+(!1===t.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return Le(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const t=n,r=new Re("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw r.file=e.filePath||void 0,r.url=qe+"#cannot-parse-style-attribute",r}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)_e.call(e,n)&&(t[Ge(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?Z[r.property]||r.property:r.attribute,n]}}function Xe(e,t,n){let r;if(n)if(t.includes(".")){const e=t.split(".");let n,o=-1;for(;++o<e.length;){const t=S(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(o&&"Literal"===t.type),optional:!1}:t}r=n}else r=S(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){const t=r.value;return _e.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);Ze(e)}function Ze(e,t){const n=new Re("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=qe+"#cannot-handle-mdx-estrees-without-createevaluater",n}function Ge(e){let t=e.replace(He,et);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function et(e){return"-"+e.toLowerCase()}const tt={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},nt={};function rt(e,t){return ot(e,"boolean"!=typeof nt.includeImageAlt||nt.includeImageAlt,"boolean"!=typeof nt.includeHtml||nt.includeHtml)}function ot(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return it(e.children,t,n)}return Array.isArray(e)?it(e,t,n):""}function it(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=ot(e[o],t,n);return r.join("")}const lt=document.createElement("i");function st(e){const t="&"+e+";";lt.innerHTML=t;const n=lt.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&(n!==t&&n)}function at(e,t,n,r){const o=e.length;let i,l=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)i=r.slice(l,l+1e4),i.unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function ct(e,t){return e.length>0?(at(e,e.length,0,t),e):t}const ut={}.hasOwnProperty;function ft(e){const t={};let n=-1;for(;++n<e.length;)dt(t,e[n]);return t}function dt(e,t){let n;for(n in t){const r=(ut.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){ut.call(r,i)||(r[i]=[]);const e=o[i];pt(r[i],Array.isArray(e)?e:e?[e]:[])}}}function pt(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);at(e,0,0,r)}function ht(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}function mt(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const gt=At(/[A-Za-z]/),yt=At(/[\dA-Za-z]/),xt=At(/[#-'*+\--9=?A-Z^-~]/);function kt(e){return null!==e&&(e<32||127===e)}const vt=At(/\d/),bt=At(/[\dA-Fa-f]/),wt=At(/[!-/:-@[-`{-~]/);function St(e){return null!==e&&e<-2}function Ct(e){return null!==e&&(e<0||32===e)}function Et(e){return-2===e||-1===e||32===e}const It=At(new RegExp("\\p{P}|\\p{S}","u")),Tt=At(/\s/);function At(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Pt(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let l="";if(37===i&&yt(e.charCodeAt(n+1))&&yt(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),o=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+o+1,l=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function Lt(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(Et(r))return e.enter(n),l(r);return t(r)};function l(r){return Et(r)&&i++<o?(e.consume(r),l):(e.exit(n),t(r))}}const Dt={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,function(n){if(null===n)return void e.consume(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Lt(e,t,"linePrefix")},function(t){return e.enter("paragraph"),r(t)});let n;return t;function r(t){const r=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=r),n=r,o(t)}function o(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):St(t)?(e.consume(t),e.exit("chunkText"),r):(e.consume(t),o)}}};const jt={tokenize:function(e){const t=this,n=[];let r,o,i,l=0;return s;function s(r){if(l<n.length){const o=n[l];return t.containerState=o[1],e.attempt(o[0].continuation,a,c)(r)}return c(r)}function a(e){if(l++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&x();const n=t.events.length;let o,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){o=t.events[i][1].end;break}y(l);let s=n;for(;s<t.events.length;)t.events[s][1].end={...o},s++;return at(t.events,i+1,0,t.events.slice(n)),t.events.length=s,c(e)}return s(e)}function c(o){if(l===n.length){if(!r)return d(o);if(r.currentConstruct&&r.currentConstruct.concrete)return h(o);t.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Ot,u,f)(o)}function u(e){return r&&x(),y(l),d(e)}function f(e){return t.parser.lazy[t.now().line]=l!==n.length,i=t.now().offset,h(e)}function d(n){return t.containerState={},e.attempt(Ot,p,h)(n)}function p(e){return l++,n.push([t.currentConstruct,t.containerState]),d(e)}function h(n){return null===n?(r&&x(),y(0),void e.consume(n)):(r=r||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(n))}function m(n){return null===n?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(n)):St(n)?(e.consume(n),g(e.exit("chunkFlow")),l=0,t.interrupt=void 0,s):(e.consume(n),m)}function g(e,n){const s=t.sliceStream(e);if(n&&s.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(s),t.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const n=t.events.length;let o,s,a=n;for(;a--;)if("exit"===t.events[a][0]&&"chunkFlow"===t.events[a][1].type){if(o){s=t.events[a][1].end;break}o=!0}for(y(l),e=n;e<t.events.length;)t.events[e][1].end={...s},e++;at(t.events,a+1,0,t.events.slice(n)),t.events.length=e}}function y(r){let o=n.length;for(;o-- >r;){const r=n[o];t.containerState=r[1],r[0].exit.call(t,e)}n.length=r}function x(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}},Ot={tokenize:function(e,t,n){return Lt(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};function Nt(e){return null===e||Ct(e)||Tt(e)?1:It(e)?2:void 0}function Mt(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const Ft={name:"attention",resolveAll:function(e,t){let n,r,o,i,l,s,a,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close)for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;s=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;const f={...e[n][1].end},d={...e[u][1].start};zt(f,-s),zt(d,s),i={type:s>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},l={type:s>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:d},o={type:s>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:s>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[n][1].end={...i.start},e[u][1].start={...l.end},a=[],e[n][1].end.offset-e[n][1].start.offset&&(a=ct(a,[["enter",e[n][1],t],["exit",e[n][1],t]])),a=ct(a,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",o,t]]),a=ct(a,Mt(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),a=ct(a,[["exit",o,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,a=ct(a,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,at(e,n-1,u-n+3,a),u=n+a.length-c-2;break}u=-1;for(;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=Nt(r);let i;return function(t){return i=t,e.enter("attentionSequence"),l(t)};function l(s){if(s===i)return e.consume(s),l;const a=e.exit("attentionSequence"),c=Nt(s),u=!c||2===c&&o||n.includes(s),f=!o||2===o&&c||n.includes(r);return a._open=Boolean(42===i?u:u&&(o||!f)),a._close=Boolean(42===i?f:f&&(c||!u)),t(s)}}};function zt(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Rt={name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(t){return gt(t)?(e.consume(t),i):64===t?n(t):a(t)}function i(e){return 43===e||45===e||46===e||yt(e)?(r=1,l(e)):a(e)}function l(t){return 58===t?(e.consume(t),r=0,s):(43===t||45===t||46===t||yt(t))&&r++<32?(e.consume(t),l):(r=0,a(t))}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||kt(r)?n(r):(e.consume(r),s)}function a(t){return 64===t?(e.consume(t),c):xt(t)?(e.consume(t),a):n(t)}function c(e){return yt(e)?u(e):n(e)}function u(n){return 46===n?(e.consume(n),r=0,c):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):f(n)}function f(t){if((45===t||yt(t))&&r++<63){const n=45===t?f:u;return e.consume(t),n}return n(t)}}};const _t={partial:!0,tokenize:function(e,t,n){return function(t){return Et(t)?Lt(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||St(e)?t(e):n(e)}}};const Bt={continuation:{tokenize:function(e,t,n){const r=this;return function(t){if(Et(t))return Lt(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t);return o(t)};function o(r){return e.attempt(Bt,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const r=this;return function(t){if(62===t){const n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),o}return n(t)};function o(n){return Et(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};const Ht={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return wt(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}};const Ut={name:"characterReference",tokenize:function(e,t,n){const r=this;let o,i,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),s};function s(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),o=31,i=yt,c(t))}function a(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=bt,c):(e.enter("characterReferenceValue"),o=7,i=vt,c(t))}function c(s){if(59===s&&l){const o=e.exit("characterReferenceValue");return i!==yt||st(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(s)}return i(s)&&l++<o?(e.consume(s),c):n(s)}}};const Vt={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(null===t)return n(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},qt={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const r=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return l;function l(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a}function a(t){return e.enter("codeFencedFence"),Et(t)?Lt(e,c,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):c(t)}function c(t){return t===i?(e.enter("codeFencedFenceSequence"),u(t)):n(t)}function u(t){return t===i?(o++,e.consume(t),u):o>=s?(e.exit("codeFencedFenceSequence"),Et(t)?Lt(e,f,"whitespace")(t):f(t)):n(t)}function f(r){return null===r||St(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}};let i,l=0,s=0;return function(t){return function(t){const n=r.events[r.events.length-1];return l=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),a(t)}(t)};function a(t){return t===i?(s++,e.consume(t),a):s<3?n(t):(e.exit("codeFencedFenceSequence"),Et(t)?Lt(e,c,"whitespace")(t):c(t))}function c(n){return null===n||St(n)?(e.exit("codeFencedFence"),r.interrupt?t(n):e.check(Vt,p,x)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),u(n))}function u(t){return null===t||St(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),c(t)):Et(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),Lt(e,f,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),u)}function f(t){return null===t||St(t)?c(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),d(t))}function d(t){return null===t||St(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),c(t)):96===t&&t===i?n(t):(e.consume(t),d)}function p(t){return e.attempt(o,x,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),m}function m(t){return l>0&&Et(t)?Lt(e,g,"linePrefix",l+1)(t):g(t)}function g(t){return null===t||St(t)?e.check(Vt,p,x)(t):(e.enter("codeFlowValue"),y(t))}function y(t){return null===t||St(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),y)}function x(n){return e.exit("codeFenced"),t(n)}}};const Wt={name:"codeIndented",tokenize:function(e,t,n){const r=this;return function(t){return e.enter("codeIndented"),Lt(e,o,"linePrefix",5)(t)};function o(e){const t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?s(t):St(t)?e.attempt($t,i,s)(t):(e.enter("codeFlowValue"),l(t))}function l(t){return null===t||St(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),l)}function s(n){return e.exit("codeIndented"),t(n)}}},$t={partial:!0,tokenize:function(e,t,n){const r=this;return o;function o(t){return r.parser.lazy[r.now().line]?n(t):St(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):Lt(e,i,"linePrefix",5)(t)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):St(e)?o(e):n(e)}}};const Yt={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(t=o;++t<r;)if("codeTextData"===e[t][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}t=o-1,r++;for(;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):t!==r&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,o,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),l(t)};function l(t){return 96===t?(e.consume(t),i++,l):(e.exit("codeTextSequence"),s(t))}function s(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),s):96===t?(o=e.enter("codeTextSequence"),r=0,c(t)):St(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(t))}function a(t){return null===t||32===t||96===t||St(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),a)}function c(n){return 96===n?(e.consume(n),r++,c):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(o.type="codeTextData",a(n))}}};class Kt{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const r=t||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&Qt(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Qt(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Qt(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);Qt(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Qt(this.left,t.reverse())}}}function Qt(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Jt(e){const t={};let n,r,o,i,l,s,a,c=-1;const u=new Kt(e);for(;++c<u.length;){for(;c in t;)c=t[c];if(n=u.get(c),c&&"chunkFlow"===n[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&(s=n[1]._tokenizer.events,o=0,o<s.length&&"lineEndingBlank"===s[o][1].type&&(o+=2),o<s.length&&"content"===s[o][1].type))for(;++o<s.length&&"content"!==s[o][1].type;)"chunkText"===s[o][1].type&&(s[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,Xt(u,c)),c=t[c],a=!0);else if(n[1]._container){for(o=c,r=void 0;o--;)if(i=u.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(r&&(u.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);else if("linePrefix"!==i[1].type&&"listItemIndent"!==i[1].type)break;r&&(n[1].end={...u.get(r)[1].start},l=u.slice(r,c),l.unshift(n),u.splice(r,c-r+1,l))}}return at(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!a}function Xt(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[];let l=n._tokenizer;l||(l=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));const s=l.events,a=[],c={};let u,f,d=-1,p=n,h=0,m=0;const g=[m];for(;p;){for(;e.get(++o)[1]!==p;);i.push(o),p._tokenizer||(u=r.sliceStream(p),p.next||u.push(null),f&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(u),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),f=p,p=p.next}for(p=n;++d<s.length;)"exit"===s[d][0]&&"enter"===s[d-1][0]&&s[d][1].type===s[d-1][1].type&&s[d][1].start.line!==s[d][1].end.line&&(m=d+1,g.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),d=g.length;d--;){const t=s.slice(g[d],g[d+1]),n=i.pop();a.push([n,n+t.length-1]),e.splice(n,2,t)}for(a.reverse(),d=-1;++d<a.length;)c[h+a[d][0]]=h+a[d][1],h+=a[d][1]-a[d][0]-1;return c}const Zt={resolve:function(e){return Jt(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?o(t):St(t)?e.check(Gt,i,o)(t):(e.consume(t),r)}function o(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Gt={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Lt(e,o,"linePrefix")};function o(o){if(null===o||St(o))return n(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}};function en(e,t,n,r,o,i,l,s,a){const c=a||Number.POSITIVE_INFINITY;let u=0;return function(t){if(60===t)return e.enter(r),e.enter(o),e.enter(i),e.consume(t),e.exit(i),f;if(null===t||32===t||41===t||kt(t))return n(t);return e.enter(r),e.enter(l),e.enter(s),e.enter("chunkString",{contentType:"string"}),h(t)};function f(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(s),f(t)):null===t||60===t||St(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(o){return u||null!==o&&41!==o&&!Ct(o)?u<c&&40===o?(e.consume(o),u++,h):41===o?(e.consume(o),u--,h):null===o||32===o||40===o||kt(o)?n(o):(e.consume(o),92===o?m:h):(e.exit("chunkString"),e.exit(s),e.exit(l),e.exit(r),t(o))}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function tn(e,t,n,r,o,i){const l=this;let s,a=0;return function(t){return e.enter(r),e.enter(o),e.consume(t),e.exit(o),e.enter(i),c};function c(f){return a>999||null===f||91===f||93===f&&!s||94===f&&!a&&"_hiddenFootnoteSupport"in l.parser.constructs?n(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),t):St(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(f))}function u(t){return null===t||91===t||93===t||St(t)||a++>999?(e.exit("chunkString"),c(t)):(e.consume(t),s||(s=!Et(t)),92===t?f:u)}function f(t){return 91===t||92===t||93===t?(e.consume(t),a++,u):u(t)}}function nn(e,t,n,r,o,i){let l;return function(t){if(34===t||39===t||40===t)return e.enter(r),e.enter(o),e.consume(t),e.exit(o),l=40===t?41:t,s;return n(t)};function s(n){return n===l?(e.enter(o),e.consume(n),e.exit(o),e.exit(r),t):(e.enter(i),a(n))}function a(t){return t===l?(e.exit(i),s(l)):null===t?n(t):St(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Lt(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===l||null===t||St(t)?(e.exit("chunkString"),a(t)):(e.consume(t),92===t?u:c)}function u(t){return t===l||92===t?(e.consume(t),c):c(t)}}function rn(e,t){let n;return function r(o){if(St(o))return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r;if(Et(o))return Lt(e,r,n?"linePrefix":"lineSuffix")(o);return t(o)}}const on={name:"definition",tokenize:function(e,t,n){const r=this;let o;return function(t){return e.enter("definition"),function(t){return tn.call(r,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return o=mt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return Ct(t)?rn(e,s)(t):s(t)}function s(t){return en(e,a,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function a(t){return e.attempt(ln,c,c)(t)}function c(t){return Et(t)?Lt(e,u,"whitespace")(t):u(t)}function u(i){return null===i||St(i)?(e.exit("definition"),r.parser.defined.push(o),t(i)):n(i)}}},ln={partial:!0,tokenize:function(e,t,n){return function(t){return Ct(t)?rn(e,r)(t):n(t)};function r(t){return nn(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function o(t){return Et(t)?Lt(e,i,"whitespace")(t):i(t)}function i(e){return null===e||St(e)?t(e):n(e)}}};const sn={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return St(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}};const an={name:"headingAtx",resolve:function(e,t){let n,r,o=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);o-2>i&&"whitespace"===e[o][1].type&&(o-=2);"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4);o>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},at(e,i,o-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]]));return e},tokenize:function(e,t,n){let r=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),o(t)}(t)};function o(t){return 35===t&&r++<6?(e.consume(t),o):null===t||Ct(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),l(n)):null===n||St(n)?(e.exit("atxHeading"),t(n)):Et(n)?Lt(e,i,"whitespace")(n):(e.enter("atxHeadingText"),s(n))}function l(t){return 35===t?(e.consume(t),l):(e.exit("atxHeadingSequence"),i(t))}function s(t){return null===t||35===t||Ct(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),s)}}};const cn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],un=["pre","script","style","textarea"],fn={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2));return e},tokenize:function(e,t,n){const r=this;let o,i,l,s,a;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),c}(t)};function c(s){return 33===s?(e.consume(s),u):47===s?(e.consume(s),i=!0,p):63===s?(e.consume(s),o=3,r.interrupt?t:N):gt(s)?(e.consume(s),l=String.fromCharCode(s),h):n(s)}function u(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,s=0,d):gt(i)?(e.consume(i),o=4,r.interrupt?t:N):n(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?t:N):n(o)}function d(o){const i="CDATA[";return o===i.charCodeAt(s++)?(e.consume(o),6===s?r.interrupt?t:I:d):n(o)}function p(t){return gt(t)?(e.consume(t),l=String.fromCharCode(t),h):n(t)}function h(s){if(null===s||47===s||62===s||Ct(s)){const a=47===s,c=l.toLowerCase();return a||i||!un.includes(c)?cn.includes(l.toLowerCase())?(o=6,a?(e.consume(s),m):r.interrupt?t(s):I(s)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(s):i?g(s):y(s)):(o=1,r.interrupt?t(s):I(s))}return 45===s||yt(s)?(e.consume(s),l+=String.fromCharCode(s),h):n(s)}function m(o){return 62===o?(e.consume(o),r.interrupt?t:I):n(o)}function g(t){return Et(t)?(e.consume(t),g):C(t)}function y(t){return 47===t?(e.consume(t),C):58===t||95===t||gt(t)?(e.consume(t),x):Et(t)?(e.consume(t),y):C(t)}function x(t){return 45===t||46===t||58===t||95===t||yt(t)?(e.consume(t),x):k(t)}function k(t){return 61===t?(e.consume(t),v):Et(t)?(e.consume(t),k):y(t)}function v(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):Et(t)?(e.consume(t),v):w(t)}function b(t){return t===a?(e.consume(t),a=null,S):null===t||St(t)?n(t):(e.consume(t),b)}function w(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||Ct(t)?k(t):(e.consume(t),w)}function S(e){return 47===e||62===e||Et(e)?y(e):n(e)}function C(t){return 62===t?(e.consume(t),E):n(t)}function E(t){return null===t||St(t)?I(t):Et(t)?(e.consume(t),E):n(t)}function I(t){return 45===t&&2===o?(e.consume(t),L):60===t&&1===o?(e.consume(t),D):62===t&&4===o?(e.consume(t),M):63===t&&3===o?(e.consume(t),N):93===t&&5===o?(e.consume(t),O):!St(t)||6!==o&&7!==o?null===t||St(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),I):(e.exit("htmlFlowData"),e.check(dn,F,T)(t))}function T(t){return e.check(pn,A,F)(t)}function A(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),P}function P(t){return null===t||St(t)?T(t):(e.enter("htmlFlowData"),I(t))}function L(t){return 45===t?(e.consume(t),N):I(t)}function D(t){return 47===t?(e.consume(t),l="",j):I(t)}function j(t){if(62===t){const n=l.toLowerCase();return un.includes(n)?(e.consume(t),M):I(t)}return gt(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),j):I(t)}function O(t){return 93===t?(e.consume(t),N):I(t)}function N(t){return 62===t?(e.consume(t),M):45===t&&2===o?(e.consume(t),N):I(t)}function M(t){return null===t||St(t)?(e.exit("htmlFlowData"),F(t)):(e.consume(t),M)}function F(n){return e.exit("htmlFlow"),t(n)}}},dn={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(_t,t,n)}}},pn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(St(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o;return n(t)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}};const hn={name:"htmlText",tokenize:function(e,t,n){const r=this;let o,i,l;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),s};function s(t){return 33===t?(e.consume(t),a):47===t?(e.consume(t),v):63===t?(e.consume(t),x):gt(t)?(e.consume(t),S):n(t)}function a(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,p):gt(t)?(e.consume(t),y):n(t)}function c(t){return 45===t?(e.consume(t),d):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),f):St(t)?(l=u,j(t)):(e.consume(t),u)}function f(t){return 45===t?(e.consume(t),d):u(t)}function d(e){return 62===e?D(e):45===e?f(e):u(e)}function p(t){const r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),6===i?h:p):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):St(t)?(l=h,j(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?D(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?D(t):St(t)?(l=y,j(t)):(e.consume(t),y)}function x(t){return null===t?n(t):63===t?(e.consume(t),k):St(t)?(l=x,j(t)):(e.consume(t),x)}function k(e){return 62===e?D(e):x(e)}function v(t){return gt(t)?(e.consume(t),b):n(t)}function b(t){return 45===t||yt(t)?(e.consume(t),b):w(t)}function w(t){return St(t)?(l=w,j(t)):Et(t)?(e.consume(t),w):D(t)}function S(t){return 45===t||yt(t)?(e.consume(t),S):47===t||62===t||Ct(t)?C(t):n(t)}function C(t){return 47===t?(e.consume(t),D):58===t||95===t||gt(t)?(e.consume(t),E):St(t)?(l=C,j(t)):Et(t)?(e.consume(t),C):D(t)}function E(t){return 45===t||46===t||58===t||95===t||yt(t)?(e.consume(t),E):I(t)}function I(t){return 61===t?(e.consume(t),T):St(t)?(l=I,j(t)):Et(t)?(e.consume(t),I):C(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,A):St(t)?(l=T,j(t)):Et(t)?(e.consume(t),T):(e.consume(t),P)}function A(t){return t===o?(e.consume(t),o=void 0,L):null===t?n(t):St(t)?(l=A,j(t)):(e.consume(t),A)}function P(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||Ct(t)?C(t):(e.consume(t),P)}function L(e){return 47===e||62===e||Ct(e)?C(e):n(e)}function D(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function j(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),O}function O(t){return Et(t)?Lt(e,N,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):N(t)}function N(t){return e.enter("htmlTextData"),l(t)}}};const mn={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",t+=e}}e.length!==n.length&&at(e,0,e.length,n);return e},resolveTo:function(e,t){let n,r,o,i,l=e.length,s=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){s=2;break}}else"labelEnd"===n.type&&(o=l);const a={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},u={type:"labelText",start:{...e[r+s+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",a,t],["enter",c,t]],i=ct(i,e.slice(r+1,r+s+3)),i=ct(i,[["enter",u,t]]),i=ct(i,Mt(t.parser.constructs.insideSpan.null,e.slice(r+s+4,o-3),t)),i=ct(i,[["exit",u,t],e[o-2],e[o-1],["exit",c,t]]),i=ct(i,e.slice(o+1)),i=ct(i,[["exit",a,t]]),at(e,r,e.length,i),e},tokenize:function(e,t,n){const r=this;let o,i,l=r.events.length;for(;l--;)if(("labelImage"===r.events[l][1].type||"labelLink"===r.events[l][1].type)&&!r.events[l][1]._balanced){o=r.events[l][1];break}return function(t){if(!o)return n(t);if(o._inactive)return u(t);return i=r.parser.defined.includes(mt(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),s};function s(t){return 40===t?e.attempt(gn,c,i?c:u)(t):91===t?e.attempt(yn,c,i?a:u)(t):i?c(t):u(t)}function a(t){return e.attempt(xn,c,u)(t)}function c(e){return t(e)}function u(e){return o._balanced=!0,n(e)}}},gn={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return Ct(t)?rn(e,o)(t):o(t)}function o(t){return 41===t?c(t):en(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return Ct(t)?rn(e,s)(t):c(t)}function l(e){return n(e)}function s(t){return 34===t||39===t||40===t?nn(e,a,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function a(t){return Ct(t)?rn(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},yn={tokenize:function(e,t,n){const r=this;return function(t){return tn.call(r,e,o,i,"reference","referenceMarker","referenceString")(t)};function o(e){return r.parser.defined.includes(mt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},xn={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}};const kn={name:"labelStartImage",resolveAll:mn.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),o};function o(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const vn={name:"labelStartLink",resolveAll:mn.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const bn={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Lt(e,t,"linePrefix")}}};const wn={name:"thematicBreak",tokenize:function(e,t,n){let r,o=0;return function(t){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(t)};function i(i){return i===r?(e.enter("thematicBreakSequence"),l(i)):o>=3&&(null===i||St(i))?(e.exit("thematicBreak"),t(i)):n(i)}function l(t){return t===r?(e.consume(t),o++,l):(e.exit("thematicBreakSequence"),Et(t)?Lt(e,i,"whitespace")(t):i(t))}}};const Sn={continuation:{tokenize:function(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(_t,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,Lt(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){if(r.containerState.furtherBlankLines||!Et(n))return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(n);return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(En,t,o)(n)});function o(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,Lt(e,e.attempt(Sn,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,l=0;return function(t){const o=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||t===r.containerState.marker:vt(t)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===t||45===t?e.check(wn,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),s(t)}return n(t)};function s(t){return vt(t)&&++l<10?(e.consume(t),s):(!r.interrupt||l<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),a(t)):n(t)}function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(_t,r.interrupt?n:c,e.attempt(Cn,f,u))}function c(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function u(t){return Et(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),f):n(t)}function f(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},Cn={partial:!0,tokenize:function(e,t,n){const r=this;return Lt(e,function(e){const o=r.events[r.events.length-1];return!Et(e)&&o&&"listItemPrefixWhitespace"===o[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},En={partial:!0,tokenize:function(e,t,n){const r=this;return Lt(e,function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}};const In={name:"setextUnderline",resolveTo:function(e,t){let n,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const l={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",l,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=l;return e.push(["exit",l,t]),e},tokenize:function(e,t,n){const r=this;let o;return function(t){let l,s=r.events.length;for(;s--;)if("lineEnding"!==r.events[s][1].type&&"linePrefix"!==r.events[s][1].type&&"content"!==r.events[s][1].type){l="paragraph"===r.events[s][1].type;break}if(!r.parser.lazy[r.now().line]&&(r.interrupt||l))return e.enter("setextHeadingLine"),o=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t);return n(t)};function i(t){return t===o?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),Et(t)?Lt(e,l,"lineSuffix")(t):l(t))}function l(r){return null===r||St(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}};const Tn={tokenize:function(e){const t=this,n=e.attempt(_t,function(r){if(null===r)return void e.consume(r);return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,Lt(e,e.attempt(this.parser.constructs.flow,r,e.attempt(Zt,r)),"linePrefix")));return n;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(r)}}};const An={resolveAll:jn()},Pn=Dn("string"),Ln=Dn("text");function Dn(e){return{resolveAll:jn("text"===e?On:void 0),tokenize:function(t){const n=this,r=this.parser.constructs[e],o=t.attempt(r,i,l);return i;function i(e){return a(e)?o(e):l(e)}function l(e){if(null!==e)return t.enter("data"),t.consume(e),s;t.consume(e)}function s(e){return a(e)?(t.exit("data"),o(e)):(t.consume(e),s)}function a(e){if(null===e)return!0;const t=r[e];let o=-1;if(t)for(;++o<t.length;){const e=t[o];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function jn(e){return function(t,n){let r,o=-1;for(;++o<=t.length;)void 0===r?t[o]&&"data"===t[o][1].type&&(r=o,o++):t[o]&&"data"===t[o][1].type||(o!==r+2&&(t[r][1].end=t[o-1][1].end,t.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(t,n):t}}function On(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const r=e[n-1][1],o=t.sliceStream(r);let i,l=o.length,s=-1,a=0;for(;l--;){const e=o[l];if("string"==typeof e){for(s=e.length;32===e.charCodeAt(s-1);)a++,s--;if(s)break;s=-1}else if(-2===e)i=!0,a++;else if(-1!==e){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(a=0),a){const o={type:n===e.length||i||a<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?s:r.start._bufferIndex+s,_index:r.start._index+l,line:r.end.line,column:r.end.column-a,offset:r.end.offset-a},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}const Nn={42:Sn,43:Sn,45:Sn,48:Sn,49:Sn,50:Sn,51:Sn,52:Sn,53:Sn,54:Sn,55:Sn,56:Sn,57:Sn,62:Bt},Mn={91:on},Fn={[-2]:Wt,[-1]:Wt,32:Wt},zn={35:an,42:wn,45:[In,wn],60:fn,61:In,95:wn,96:qt,126:qt},Rn={38:Ut,92:Ht},_n={[-5]:bn,[-4]:bn,[-3]:bn,33:kn,38:Ut,42:Ft,60:[Rt,hn],91:vn,92:[sn,Ht],93:mn,95:Ft,96:Yt},Bn={null:[Ft,An]},Hn=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:{null:[42,95]},contentInitial:Mn,disable:{null:[]},document:Nn,flow:zn,flowInitial:Fn,insideSpan:Bn,string:Rn,text:_n},Symbol.toStringTag,{value:"Module"}));function Un(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let l=[],s=[];const a={attempt:g(function(e,t){y(e,t.from)}),check:g(m),consume:function(e){St(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,x()):-1!==e&&(r.column++,r.offset++);r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++));c.previous=e},enter:function(e,t){const n=t||{};return n.type=e,n.start=d(),c.events.push(["enter",n,c]),s.push(n),n},exit:function(e){const t=s.pop();return t.end=d(),c.events.push(["exit",t,c]),t},interrupt:g(m,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,x()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=t?" ":"\t";break;case-1:if(!t&&o)continue;l=" ";break;default:l=String.fromCharCode(i)}o=-2===i,r.push(l)}return r.join("")}(f(e),t)},sliceStream:f,write:function(e){if(l=ct(l,e),p(),null!==l[l.length-1])return[];return y(t,0),c.events=Mt(i,c.events,c),c.events}};let u=t.tokenize.call(c,a);return t.resolveAll&&i.push(t),c;function f(e){return function(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let l;if(n===o)l=[e[n].slice(r,i)];else{if(l=e.slice(n,o),r>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(r):l.shift()}i>0&&l.push(e[o].slice(0,i))}return l}(l,e)}function d(){const{_bufferIndex:e,_index:t,line:n,column:o,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:o,offset:i}}function p(){let e;for(;r._index<l.length;){const t=l[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;)h(t.charCodeAt(r._bufferIndex));else h(t)}}function h(e){u=u(e)}function m(e,t){t.restore()}function g(e,t){return function(n,o,i){let l,u,f,p;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){return t;function t(t){const n=null!==t&&e[t],r=null!==t&&e.null;return h([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(r)?r:r?[r]:[]])(t)}}(n);function h(e){return l=e,u=0,0===e.length?i:m(e[u])}function m(e){return function(n){p=function(){const e=d(),t=c.previous,n=c.currentConstruct,o=c.events.length,i=Array.from(s);return{from:o,restore:l};function l(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=o,s=i,x()}}(),f=e,e.partial||(c.currentConstruct=e);if(e.name&&c.parser.constructs.disable.null.includes(e.name))return y();return e.tokenize.call(t?Object.assign(Object.create(c),t):c,a,g,y)(n)}}function g(t){return e(f,p),o}function y(e){return p.restore(),++u<l.length?m(l[u]):i}}}function y(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&at(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function x(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}const Vn=/[\0\t\n\r]/g;const qn=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Wn(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return ht(n.slice(t?2:1),t?16:10)}return st(n)||e}const $n={}.hasOwnProperty;function Yn(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:E,autolinkEmail:E,atxHeading:i(X),blockQuote:i($),characterEscape:E,characterReference:E,codeFenced:i(Y),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(Y,l),codeText:i(K,l),codeTextData:E,data:E,codeFlowValue:E,definition:i(Q),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(J),hardBreakEscape:i(Z),hardBreakTrailing:i(Z),htmlFlow:i(G,l),htmlFlowData:E,htmlText:i(G,l),htmlTextData:E,image:i(ee),label:l,link:i(te),listItem:i(re),listItemValue:d,listOrdered:i(ne,f),listUnordered:i(ne),paragraph:i(oe),reference:_,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(X),strong:i(ie),thematicBreak:i(se)},exit:{atxHeading:a(),atxHeadingSequence:b,autolink:a(),autolinkEmail:W,autolinkProtocol:q,blockQuote:a(),characterEscapeValue:I,characterReferenceMarkerHexadecimal:H,characterReferenceMarkerNumeric:H,characterReferenceValue:U,characterReference:V,codeFenced:a(g),codeFencedFence:m,codeFencedFenceInfo:p,codeFencedFenceMeta:h,codeFlowValue:I,codeIndented:a(y),codeText:a(D),codeTextData:I,data:I,definition:a(),definitionDestinationString:v,definitionLabelString:x,definitionTitleString:k,emphasis:a(),hardBreakEscape:a(A),hardBreakTrailing:a(A),htmlFlow:a(P),htmlFlowData:I,htmlText:a(L),htmlTextData:I,image:a(O),label:M,labelText:N,lineEnding:T,link:a(j),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:B,resourceDestinationString:F,resourceTitleString:z,resource:R,setextHeading:a(C),setextHeadingLineSequence:S,setextHeadingText:w,strong:a(),thematicBreak:a()}};Qn(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(e){let r={type:"root",children:[]};const i={stack:[r],tokenStack:[],config:t,enter:s,exit:c,buffer:l,resume:u,data:n},a=[];let f=-1;for(;++f<e.length;)if("listOrdered"===e[f][1].type||"listUnordered"===e[f][1].type)if("enter"===e[f][0])a.push(f);else{f=o(e,a.pop(),f)}for(f=-1;++f<e.length;){const n=t[e[f][0]];$n.call(n,e[f][1].type)&&n[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},i),e[f][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||Xn).call(i,void 0,e[0])}for(r.position={start:Kn(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:Kn(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<t.transforms.length;)r=t.transforms[f](r)||r;return r}function o(e,t,n){let r,o,i,l,s=t-1,a=-1,c=!1;for(;++s<=n;){const t=e[s];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?a++:a--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||a||i||(i=s),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!a&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===a&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=s;for(o=void 0;l--;){const t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;o&&(e[o][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",o=l}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:t[1].end),e.splice(o||s,0,["exit",r,t[2]]),s++,n++}if("listItemPrefix"===t[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=o,e.splice(s,0,["enter",o,t[2]]),s++,n++,i=void 0,l=!0}}}return e[t][1]._spread=c,n}function i(e,t){return n;function n(n){s.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function s(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:Kn(t.start),end:void 0}}function a(e){return t;function t(t){e&&e.call(this,t),c.call(this,t)}}function c(e,t){const n=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+Ne({start:e.start,end:e.end})+"): it’s not open");if(r[0].type!==e.type)if(t)t.call(this,e,r[0]);else{(r[1]||Xn).call(this,e,r[0])}n.position.end=Kn(e.end)}function u(){return rt(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function d(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function p(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function h(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function m(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function x(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=mt(this.sliceSerialize(e)).toLowerCase()}function k(){const e=this.resume();this.stack[this.stack.length-1].title=e}function v(){const e=this.resume();this.stack[this.stack.length-1].url=e}function b(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function S(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function C(){this.data.setextHeadingSlurpLineEnding=void 0}function E(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n=le(),n.position={start:Kn(e.start),end:void 0},t.push(n)),this.stack.push(n)}function I(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=Kn(e.end)}function T(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak){return n.children[n.children.length-1].position.end=Kn(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(E.call(this,e),I.call(this,e))}function A(){this.data.atHardBreak=!0}function P(){const e=this.resume();this.stack[this.stack.length-1].value=e}function L(){const e=this.resume();this.stack[this.stack.length-1].value=e}function D(){const e=this.resume();this.stack[this.stack.length-1].value=e}function j(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function O(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function N(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(qn,Wn)}(t),n.identifier=mt(t).toLowerCase()}function M(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t}function F(){const e=this.resume();this.stack[this.stack.length-1].url=e}function z(){const e=this.resume();this.stack[this.stack.length-1].title=e}function R(){this.data.inReference=void 0}function _(){this.data.referenceType="collapsed"}function B(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=mt(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function H(e){this.data.characterReferenceType=e.type}function U(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let r;if(n)r=ht(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0;else{r=st(t)}this.stack[this.stack.length-1].value+=r}function V(e){this.stack.pop().position.end=Kn(e.end)}function q(e){I.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function W(e){I.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function $(){return{type:"blockquote",children:[]}}function Y(){return{type:"code",lang:null,meta:null,value:""}}function K(){return{type:"inlineCode",value:""}}function Q(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function J(){return{type:"emphasis",children:[]}}function X(){return{type:"heading",depth:0,children:[]}}function Z(){return{type:"break"}}function G(){return{type:"html",value:""}}function ee(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function ne(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function re(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function oe(){return{type:"paragraph",children:[]}}function ie(){return{type:"strong",children:[]}}function le(){return{type:"text",value:""}}function se(){return{type:"thematicBreak"}}}(n)(function(e){for(;!Jt(e););return e}(function(e){const t={constructs:ft([Hn,...(e||{}).extensions||[]]),content:n(Dt),defined:[],document:n(jt),flow:n(Tn),lazy:{},string:n(Pn),text:n(Ln)};return t;function n(e){return function(n){return Un(t,e,n)}}}(n).document().write(function(){let e,t=1,n="",r=!0;return function(o,i,l){const s=[];let a,c,u,f,d;for(o=n+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),u=0,n="",r&&(65279===o.charCodeAt(0)&&u++,r=void 0);u<o.length;){if(Vn.lastIndex=u,a=Vn.exec(o),f=a&&void 0!==a.index?a.index:o.length,d=o.charCodeAt(f),!a){n=o.slice(u);break}if(10===d&&u===f&&e)s.push(-3),e=void 0;else switch(e&&(s.push(-5),e=void 0),u<f&&(s.push(o.slice(u,f)),t+=f-u),d){case 0:s.push(65533),t++;break;case 9:for(c=4*Math.ceil(t/4),s.push(-2);t++<c;)s.push(-1);break;case 10:s.push(-4),t=1;break;default:e=!0,t=1}u=f+1}return l&&(e&&s.push(-5),n&&s.push(n),s.push(null)),s}}()(e,t,!0))))}function Kn(e){return{line:e.line,column:e.column,offset:e.offset}}function Qn(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Qn(e,r):Jn(e,r)}}function Jn(e,t){let n;for(n in t)if($n.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Xn(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Ne({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Ne({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Ne({start:t.start,end:t.end})+") is still open")}function Zn(e){const t=this;t.parser=function(n){return Yn(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Gn(e,t){const n=t.referenceType;let r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const l=o[o.length-1];return l&&"text"===l.type?l.value+=r:o.push({type:"text",value:r}),o}function er(e){const t=e.spread;return null==t?e.children.length>1:t}function tr(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(nr(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(nr(t.slice(o),o>0,!1)),i.join("")}function nr(e,t,n){let r=0,o=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(o-1);for(;9===t||32===t;)o--,t=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const rr={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=Pt(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let l,s=e.footnoteCounts.get(r);void 0===s?(s=0,e.footnoteOrder.push(r),l=e.footnoteOrder.length):l=i+1,s+=1,e.footnoteCounts.set(r,s);const a={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,a);const c={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Gn(e,t);const o={src:Pt(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:Pt(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Gn(e,t);const o={href:Pt(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:Pt(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){const r=e.all(t),o=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=er(n[r])}return t}(n):er(t),i={},l=[];if("boolean"==typeof t.checked){const e=r[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},r.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const e=r[s];(o||0!==s||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?l.push(e):l.push(...e.children)}const a=r[r.length-1];a&&(o||"element"!==a.type||"p"!==a.tagName)&&l.push({type:"text",value:"\n"});const c={type:"element",tagName:"li",properties:i,children:l};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){const n={},r=e.all(t);let o=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),o.push(n)}if(n.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=je(t.children[1]),l=De(t.children[t.children.length-1]);i&&l&&(r.position={start:i,end:l}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const r=n?n.children:void 0,o=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,l=i?i.length:t.children.length;let s=-1;const a=[];for(;++s<l;){const n=t.children[s],r={},l=i?i[s]:void 0;l&&(r.align=l);let c={type:"element",tagName:o,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),a.push(c)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(a,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){const n={type:"text",value:tr(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:or,yaml:or,definition:or,footnoteDefinition:or};function or(){}const ir="object"==typeof self?self:globalThis,lr=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);const[i,l]=t[o];switch(i){case 0:case-1:return n(l,o);case 1:{const e=n([],o);for(const t of l)e.push(r(t));return e}case 2:{const e=n({},o);for(const[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),o);case 4:{const{source:e,flags:t}=l;return n(new RegExp(e,t),o)}case 5:{const e=n(new Map,o);for(const[t,n]of l)e.set(r(t),r(n));return e}case 6:{const e=n(new Set,o);for(const t of l)e.add(r(t));return e}case 7:{const{name:e,message:t}=l;return n(new ir[e](t),o)}case 8:return n(BigInt(l),o);case"BigInt":return n(Object(BigInt(l)),o);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{const{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new ir[i](l),o)};return r})(new Map,e)(0),sr="",{toString:ar}={},{keys:cr}=Object,ur=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=ar.call(e).slice(8,-1);switch(n){case"Array":return[1,sr];case"Object":return[2,sr];case"Date":return[3,sr];case"RegExp":return[4,sr];case"Map":return[5,sr];case"Set":return[6,sr];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},fr=([e,t])=>0===e&&("function"===t||"symbol"===t),dr=(e,{json:t,lossy:n}={})=>{const r=[];return((e,t,n,r)=>{const o=(e,t)=>{const o=r.push(e)-1;return n.set(t,o),o},i=r=>{if(n.has(r))return n.get(r);let[l,s]=ur(r);switch(l){case 0:{let t=r;switch(s){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+s);t=null;break;case"undefined":return o([-1],r)}return o([l,t],r)}case 1:{if(s){let e=r;return"DataView"===s?e=new Uint8Array(r.buffer):"ArrayBuffer"===s&&(e=new Uint8Array(r)),o([s,[...e]],r)}const e=[],t=o([l,e],r);for(const n of r)e.push(i(n));return t}case 2:{if(s)switch(s){case"BigInt":return o([s,r.toString()],r);case"Boolean":case"Number":case"String":return o([s,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());const n=[],a=o([l,n],r);for(const t of cr(r))!e&&fr(ur(r[t]))||n.push([i(t),i(r[t])]);return a}case 3:return o([l,r.toISOString()],r);case 4:{const{source:e,flags:t}=r;return o([l,{source:e,flags:t}],r)}case 5:{const t=[],n=o([l,t],r);for(const[o,l]of r)(e||!fr(ur(o))&&!fr(ur(l)))&&t.push([i(o),i(l)]);return n}case 6:{const t=[],n=o([l,t],r);for(const o of r)!e&&fr(ur(o))||t.push(i(o));return n}}const{message:a}=r;return o([l,{name:s,message:a}],r)};return i})(!(t||n),!!t,new Map,r)(e),r},pr="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?lr(dr(e,t)):structuredClone(e):(e,t)=>lr(dr(e,t));function hr(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function mr(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const gr=function(e){if(null==e)return xr;if("function"==typeof e)return yr(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=gr(e[n]);return yr(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return yr(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return yr(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function yr(e){return function(t,n,r){return Boolean(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function xr(){return!0}const kr=[],vr=!0,br=!1;function wr(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=gr(o),l=r?-1:1;!function e(o,s,a){const c=o&&"object"==typeof o?o:{};if("string"==typeof c.type){const e="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(u,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return u;function u(){let c,u,f,d=kr;if((!t||i(o,s,a[a.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[vr,e];return null==e?kr:[e]}(n(o,a)),d[0]===br))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(u=(r?t.children.length:-1)+l,f=a.concat(t);u>-1&&u<t.children.length;){const n=t.children[u];if(c=e(n,u,f)(),c[0]===br)return c;u="number"==typeof c[1]?c[1]:u+l}}return d}}(e,void 0,[])()}function Sr(e,t,n,r){let o,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,o=n):(i=t,l=n,o=r),wr(e,i,function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)},o)}const Cr={}.hasOwnProperty,Er={};function Ir(e,t){e.position&&(t.position=function(e){const t=je(e),n=De(e);if(t&&n)return{start:t,end:n}}(e))}function Tr(e,t){let n=t;if(e&&e.data){const t=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;if("string"==typeof t)if("element"===n.type)n.tagName=t;else{n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}}"element"===n.type&&o&&Object.assign(n.properties,pr(o)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function Ar(e,t){const n=t.data||{},r=!("value"in t)||Cr.call(n,"hProperties")||Cr.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,r),e.applyData(t,r)}function Pr(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function Lr(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function Dr(e,t){const n=function(e,t){const n=t||Er,r=new Map,o=new Map,i=new Map,l={...rr,...n.handlers},s={all:function(e){const t=[];if("children"in e){const n=e.children;let r=-1;for(;++r<n.length;){const o=s.one(n[r],e);if(o){if(r&&"break"===n[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=Lr(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=Lr(e.value))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t},applyData:Tr,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,t){const n=e.type,r=s.handlers[n];if(Cr.call(s.handlers,n)&&r)return r(s,e,t);if(s.options.passThrough&&s.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,r=pr(n);return r.children=s.all(e),r}return pr(e)}return(s.options.unknownHandler||Ar)(s,e,t)},options:n,patch:Ir,wrap:Pr};return Sr(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?r:o,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),s}(e,t),r=n.one(e,void 0),o=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||hr,r=e.options.footnoteBackLabel||mr,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let a=-1;for(;++a<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[a]);if(!o)continue;const i=e.all(o),l=String(o.identifier).toUpperCase(),c=Pt(l.toLowerCase());let u=0;const f=[],d=e.footnoteCounts.get(l);for(;void 0!==d&&++u<=d;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(a,u);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(a,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){const e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...f)}else i.push(...f);const h={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(i,!0)};e.patch(o,h),s.push(h)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...pr(l),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:"\n"},o),i}function jr(e,t){return e&&"run"in e?async function(n,r){const o=Dr(n,{file:r,...t});await e.run(o,r)}:function(n,r){return Dr(n,{file:r,...e||t})}}function Or(e){if(e)throw e}var Nr=Object.prototype.hasOwnProperty,Mr=Object.prototype.toString,Fr=Object.defineProperty,zr=Object.getOwnPropertyDescriptor,Rr=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Mr.call(e)},_r=function(e){if(!e||"[object Object]"!==Mr.call(e))return!1;var t,n=Nr.call(e,"constructor"),r=e.constructor&&e.constructor.prototype&&Nr.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!r)return!1;for(t in e);return void 0===t||Nr.call(e,t)},Br=function(e,t){Fr&&"__proto__"===t.name?Fr(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},Hr=function(e,t){if("__proto__"===t){if(!Nr.call(e,t))return;if(zr)return zr(e,t).value}return e[t]};const Ur=r(function e(){var t,n,r,o,i,l,s=arguments[0],a=1,c=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[1]||{},a=2),(null==s||"object"!=typeof s&&"function"!=typeof s)&&(s={});a<c;++a)if(null!=(t=arguments[a]))for(n in t)r=Hr(s,n),s!==(o=Hr(t,n))&&(u&&o&&(_r(o)||(i=Rr(o)))?(i?(i=!1,l=r&&Rr(r)?r:[]):l=r&&_r(r)?r:{},Br(s,{name:n,newValue:e(u,l,o)})):void 0!==o&&Br(s,{name:n,newValue:o}));return s});function Vr(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}function qr(){const e=[],t={run:function(...t){let n=-1;const r=t.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...l){const s=e[++n];let a=-1;if(i)r(i);else{for(;++a<t.length;)null!==l[a]&&void 0!==l[a]||(l[a]=t[a]);t=l,s?function(e,t){let n;return r;function r(...t){const r=e.length>t.length;let s;r&&t.push(o);try{s=e.apply(this,t)}catch(i){if(r&&n)throw i;return o(i)}r||(s&&s.then&&"function"==typeof s.then?s.then(l,o):s instanceof Error?o(s):l(s))}function o(e,...r){n||(n=!0,t(e,...r))}function l(e){o(null,e)}}(s,o)(...l):r(null,...l)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}const Wr={basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');$r(e);let n,r=0,o=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else o<0&&(n=!0,o=i+1);return o<0?"":e.slice(r,o)}if(t===e)return"";let l=-1,s=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),s>-1&&(e.codePointAt(i)===t.codePointAt(s--)?s<0&&(o=i):(s=-1,o=l));r===o?o=l:o<0&&(o=e.length);return e.slice(r,o)},dirname:function(e){if($r(e),0===e.length)return".";let t,n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){$r(e);let t,n=e.length,r=-1,o=0,i=-1,l=0;for(;n--;){const s=e.codePointAt(n);if(47!==s)r<0&&(t=!0,r=n+1),46===s?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1);else if(t){o=n+1;break}}if(i<0||r<0||0===l||1===l&&i===r-1&&i===o+1)return"";return e.slice(i,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)$r(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){$r(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,r,o="",i=0,l=-1,s=0,a=-1;for(;++a<=e.length;){if(a<e.length)n=e.codePointAt(a);else{if(47===n)break;n=47}if(47===n){if(l===a-1||1===s);else if(l!==a-1&&2===s){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),l=a,s=0;continue}}else if(o.length>0){o="",i=0,l=a,s=0;continue}t&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(l+1,a):o=e.slice(l+1,a),i=a-l-1;l=a,s=0}else 46===n&&s>-1?s++:s=-1}return o}(e,!t);0!==n.length||t||(n=".");n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/");return t?"/"+n:n}(t)},sep:"/"};function $r(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Yr={cwd:function(){return"/"}};function Kr(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function Qr(e){if("string"==typeof e)e=new URL(e);else if(!Kr(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}const Jr=["history","path","basename","stem","extname","dirname"];class Xr{constructor(e){let t;t=e?Kr(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":Yr.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,r=-1;for(;++r<Jr.length;){const e=Jr[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)Jr.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?Wr.basename(this.path):void 0}set basename(e){Gr(e,"basename"),Zr(e,"basename"),this.path=Wr.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?Wr.dirname(this.path):void 0}set dirname(e){eo(this.basename,"dirname"),this.path=Wr.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?Wr.extname(this.path):void 0}set extname(e){if(Zr(e,"extname"),eo(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Wr.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Kr(e)&&(e=Qr(e)),Gr(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?Wr.basename(this.path,this.extname):void 0}set stem(e){Gr(e,"stem"),Zr(e,"stem"),this.path=Wr.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){const r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){const r=new Re(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function Zr(e,t){if(e&&e.includes(Wr.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Wr.sep+"`")}function Gr(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function eo(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const to=function(e){const t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},no={}.hasOwnProperty;class ro extends to{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=qr()}copy(){const e=new ro;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(Ur(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(so("data",this.frozen),this.namespace[e]=t,this):no.call(this.namespace,e)&&this.namespace[e]||void 0:e?(so("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const r=t.call(e,...n);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=uo(e),n=this.parser||this.Parser;return io("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),io("process",this.parser||this.Parser),lo("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){const i=uo(e),l=n.parse(i);function s(e,n){e||!n?o(e):r?r(n):t(void 0,n)}n.run(l,i,function(e,t,r){if(e||!t||!r)return s(e);const o=t,i=n.stringify(o,r);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?r.value=i:r.result=i,s(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),io("processSync",this.parser||this.Parser),lo("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,Or(e),t=r}),co("processSync","process",n),t}run(e,t,n){ao(e),this.freeze();const r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,i){const l=uo(t);r.run(e,l,function(t,r,l){const s=r||e;t?i(t):o?o(s):n(void 0,s,l)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){Or(e),n=t,r=!0}),co("runSync","run",r),n}stringify(e,t){this.freeze();const n=uo(t),r=this.compiler||this.Compiler;return lo("stringify",r),ao(e),r(e,n)}use(e,...t){const n=this.attachers,r=this.namespace;if(so("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function o(e){if("function"==typeof e)s(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;s(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=Ur(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;){o(e[t])}}}function s(e,t){let r=-1,o=-1;for(;++r<n.length;)if(n[r][0]===e){o=r;break}if(-1===o)n.push([e,...t]);else if(t.length>0){let[r,...i]=t;const l=n[o][1];Vr(l)&&Vr(r)&&(r=Ur(!0,l,r)),n[o]=[e,r,...i]}}}}const oo=(new ro).freeze();function io(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function lo(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function so(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function ao(e){if(!Vr(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function co(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function uo(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new Xr(e)}const fo=[],po={allowDangerousHtml:!0},ho=/^(https?|ircs?|mailto|xmpp)$/i,mo=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function go(e){const t=function(e){const t=e.rehypePlugins||fo,n=e.remarkPlugins||fo,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...po}:po,o=oo().use(Zn).use(n).use(jr,r).use(t);return o}(e),n=function(e){const t=e.children||"",n=new Xr;"string"==typeof t&&(n.value=t);return n}(e);return function(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,l=t.skipHtml,s=t.unwrapDisallowed,a=t.urlTransform||yo;for(const u of mo)Object.hasOwn(t,u.from)&&k((u.from,u.to&&u.to,u.id));t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]});return Sr(e,c),We(e,{Fragment:h.Fragment,components:o,ignoreInvalidStyle:!0,jsx:h.jsx,jsxs:h.jsxs,passKeys:!0,passNode:!0});function c(e,t,o){if("raw"===e.type&&o&&"number"==typeof t)return l?o.children.splice(t,1):o.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in tt)if(Object.hasOwn(tt,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],r=tt[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=a(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,o)),l&&o&&"number"==typeof t)return s&&e.children?o.children.splice(t,1,...e.children):o.children.splice(t,1),t}}}(t.runSync(t.parse(n),n),e)}function yo(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return-1===t||-1!==o&&t>o||-1!==n&&t>n||-1!==r&&t>r||ho.test(e.slice(0,t))?e:""}function xo(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;-1!==o;)r++,o=n.indexOf(t,o+t.length);return r}function ko(e,t,n){const r=gr((n||{}).ignore||[]),o=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([vo(e[0]),bo(e[1])])}return t}(t);let i=-1;for(;++i<o.length;)wr(e,"text",l);function l(e,t){let n,l=-1;for(;++l<t.length;){const e=t[l],o=n?n.children:void 0;if(r(e,o?o.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=o[i][0],l=o[i][1];let s=0;const a=n.children.indexOf(e);let c=!1,u=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,o={index:f.index,input:f.input,stack:[...t,e]};let i=l(...f,o);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?r.lastIndex=n+1:(s!==n&&u.push({type:"text",value:e.value.slice(s,n)}),Array.isArray(i)?u.push(...i):i&&u.push(i),s=n+f[0].length,c=!0),!r.global)break;f=r.exec(e.value)}c?(s<e.value.length&&u.push({type:"text",value:e.value.slice(s)}),n.children.splice(a,1,...u)):u=[e];return a+u.length}(e,t)}}function vo(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function bo(e){return"function"==typeof e?e:function(){return e}}const wo="phrasing",So=["autolink","link","image","label"];function Co(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function Eo(e){this.config.enter.autolinkProtocol.call(this,e)}function Io(e){this.config.exit.autolinkProtocol.call(this,e)}function To(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function Ao(e){this.config.exit.autolinkEmail.call(this,e)}function Po(e){this.exit(e)}function Lo(e){ko(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,Do],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),jo]],{ignore:["link","linkReference"]})}function Do(e,t,n,r,o){let i="";if(!Oo(o))return!1;if(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const l=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const o=xo(e,"(");let i=xo(e,")");for(;-1!==r&&o>i;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),i++;return[e,n]}(n+r);if(!l[0])return!1;const s={type:"link",title:null,url:i+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[s,{type:"text",value:l[1]}]:s}function jo(e,t,n,r){return!(!Oo(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function Oo(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||Tt(n)||It(n))&&(!t||47!==n)}function No(){this.buffer()}function Mo(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function Fo(){this.buffer()}function zo(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function Ro(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=mt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function _o(e){this.exit(e)}function Bo(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=mt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Ho(e){this.exit(e)}function Uo(e,t,n,r){const o=n.createTracker(r);let i=o.move("[^");const l=n.enter("footnoteReference"),s=n.enter("reference");return i+=o.move(n.safe(n.associationId(e),{after:"]",before:i})),s(),l(),i+=o.move("]"),i}function Vo(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,o){const i=r.createTracker(o);let l=i.move("[^");const s=r.enter("footnoteDefinition"),a=r.enter("label");l+=i.move(r.safe(r.associationId(e),{before:l,after:"]"})),a(),l+=i.move("]:"),e.children&&e.children.length>0&&(i.shift(4),l+=i.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,i.current()),t?Wo:qo)));return s(),l},footnoteReference:Uo},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function qo(e,t,n){return 0===t?e:Wo(e,t,n)}function Wo(e,t,n){return(n?"":"    ")+e}Uo.peek=function(){return"["};const $o=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function Yo(e){this.enter({type:"delete",children:[]},e)}function Ko(e){this.exit(e)}function Qo(e,t,n,r){const o=n.createTracker(r),i=n.enter("strikethrough");let l=o.move("~~");return l+=n.containerPhrasing(e,{...o.current(),before:l,after:"~"}),l+=o.move("~~"),i(),l}function Jo(e){return e.length}function Xo(e){return null==e?"":String(e)}function Zo(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function Go(e,t,n){return">"+(n?"":" ")+e}function ei(e,t){return ti(e,t.inConstruct,!0)&&!ti(e,t.notInConstruct,!1)}function ti(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function ni(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&ei(n.stack,n.unsafe[o]))return/[ \t]/.test(r.before)?"":" ";return"\\\n"}function ri(e,t,n){return(n?"":"    ")+e}function oi(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function ii(e){return"&#x"+e.toString(16).toUpperCase()+";"}function li(e,t,n){const r=Nt(e),o=Nt(t);return void 0===r?void 0===o?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function si(e,t,n,r){const o=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),l=n.createTracker(r),s=l.move(o);let a=l.move(n.containerPhrasing(e,{after:o,before:s,...l.current()}));const c=a.charCodeAt(0),u=li(r.before.charCodeAt(r.before.length-1),c,o);u.inside&&(a=ii(c)+a.slice(1));const f=a.charCodeAt(a.length-1),d=li(r.after.charCodeAt(0),f,o);d.inside&&(a=a.slice(0,-1)+ii(f));const p=l.move(o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:u.outside},s+a+p}function ai(e){return e.value||""}function ci(e,t,n,r){const o=oi(n),i='"'===o?"Quote":"Apostrophe",l=n.enter("image");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("![");return c+=a.move(n.safe(e.alt,{before:c,after:"]",...a.current()})),c+=a.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":")",...a.current()}))),s(),e.title&&(s=n.enter(`title${i}`),c+=a.move(" "+o),c+=a.move(n.safe(e.title,{before:c,after:o,...a.current()})),c+=a.move(o),s()),c+=a.move(")"),l(),c}function ui(e,t,n,r){const o=e.referenceType,i=n.enter("imageReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("![");const c=n.safe(e.alt,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,i(),"full"!==o&&c&&c===f?"shortcut"===o?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function fi(e,t,n){let r=e.value||"",o="`",i=-1;for(;new RegExp("(^|[^`])"+o+"([^`]|$)").test(r);)o+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let o;if(e.atBreak)for(;o=t.exec(r);){let e=o.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(o.index+1)}}return o+r+o}function di(e,t){const n=rt(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function pi(e,t,n,r){const o=oi(n),i='"'===o?"Quote":"Apostrophe",l=n.createTracker(r);let s,a;if(di(e,n)){const t=n.stack;n.stack=[],s=n.enter("autolink");let r=l.move("<");return r+=l.move(n.containerPhrasing(e,{before:r,after:">",...l.current()})),r+=l.move(">"),s(),n.stack=t,r}s=n.enter("link"),a=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()})),c+=l.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(a=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),a(),e.title&&(a=n.enter(`title${i}`),c+=l.move(" "+o),c+=l.move(n.safe(e.title,{before:c,after:o,...l.current()})),c+=l.move(o),a()),c+=l.move(")"),s(),c}function hi(e,t,n,r){const o=e.referenceType,i=n.enter("linkReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("[");const c=n.containerPhrasing(e,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,i(),"full"!==o&&c&&c===f?"shortcut"===o?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function mi(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function gi(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}Qo.peek=function(){return"~"},si.peek=function(e,t,n){return n.options.emphasis||"*"},ai.peek=function(){return"<"},ci.peek=function(){return"!"},ui.peek=function(){return"!"},fi.peek=function(){return"`"},pi.peek=function(e,t,n){return di(e,n)?"<":"["},hi.peek=function(){return"["};const yi=gr(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function xi(e,t,n,r){const o=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),l=n.createTracker(r),s=l.move(o+o);let a=l.move(n.containerPhrasing(e,{after:o,before:s,...l.current()}));const c=a.charCodeAt(0),u=li(r.before.charCodeAt(r.before.length-1),c,o);u.inside&&(a=ii(c)+a.slice(1));const f=a.charCodeAt(a.length-1),d=li(r.after.charCodeAt(0),f,o);d.inside&&(a=a.slice(0,-1)+ii(f));const p=l.move(o+o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:u.outside},s+a+p}xi.peek=function(e,t,n){return n.options.strong||"*"};const ki={blockquote:function(e,t,n,r){const o=n.enter("blockquote"),i=n.createTracker(r);i.move("> "),i.shift(2);const l=n.indentLines(n.containerFlow(e,i.current()),Go);return o(),l},break:ni,code:function(e,t,n,r){const o=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",l="`"===o?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,ri);return e(),t}const s=n.createTracker(r),a=o.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,l=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>l&&(l=i):i=1,o=r+t.length,r=n.indexOf(t,o);return l}(i,o)+1,3)),c=n.enter("codeFenced");let u=s.move(a);if(e.lang){const t=n.enter(`codeFencedLang${l}`);u+=s.move(n.safe(e.lang,{before:u,after:" ",encode:["`"],...s.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${l}`);u+=s.move(" "),u+=s.move(n.safe(e.meta,{before:u,after:"\n",encode:["`"],...s.current()})),t()}return u+=s.move("\n"),i&&(u+=s.move(i+"\n")),u+=s.move(a),c(),u},definition:function(e,t,n,r){const o=oi(n),i='"'===o?"Quote":"Apostrophe",l=n.enter("definition");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("[");return c+=a.move(n.safe(n.associationId(e),{before:c,after:"]",...a.current()})),c+=a.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...a.current()}))),s(),e.title&&(s=n.enter(`title${i}`),c+=a.move(" "+o),c+=a.move(n.safe(e.title,{before:c,after:o,...a.current()})),c+=a.move(o),s()),l(),c},emphasis:si,hardBreak:ni,heading:function(e,t,n,r){const o=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(r);if(function(e,t){let n=!1;return Sr(e,function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,br}),Boolean((!e.depth||e.depth<3)&&rt(e)&&(t.options.setext||n))}(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),l=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return r(),t(),l+"\n"+(1===o?"=":"-").repeat(l.length-(Math.max(l.lastIndexOf("\r"),l.lastIndexOf("\n"))+1))}const l="#".repeat(o),s=n.enter("headingAtx"),a=n.enter("phrasing");i.move(l+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(c)&&(c=ii(c.charCodeAt(0))+c.slice(1)),c=c?l+" "+c:l,n.options.closeAtx&&(c+=" "+l),a(),s(),c},html:ai,image:ci,imageReference:ui,inlineCode:fi,link:pi,linkReference:hi,list:function(e,t,n,r){const o=n.enter("list"),i=n.bulletCurrent;let l=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):mi(n);const s=e.ordered?"."===l?")":".":function(e){const t=mi(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let a=!(!t||!n.bulletLastUsed)&&l===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==l&&"-"!==l||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(a=!0),gi(n)===l&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){a=!0;break}}}}a&&(l=s),n.bulletCurrent=l;const c=n.containerFlow(e,r);return n.bulletLastUsed=l,n.bulletCurrent=i,o(),c},listItem:function(e,t,n,r){const o=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||mi(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let l=i.length+1;("tab"===o||"mixed"===o&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));const s=n.createTracker(r);s.move(i+" ".repeat(l-i.length)),s.shift(l);const a=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,s.current()),function(e,t,n){if(t)return(n?"":" ".repeat(l))+e;return(n?i:i+" ".repeat(l-i.length))+e});return a(),c},paragraph:function(e,t,n,r){const o=n.enter("paragraph"),i=n.enter("phrasing"),l=n.containerPhrasing(e,r);return i(),o(),l},root:function(e,t,n,r){return(e.children.some(function(e){return yi(e)})?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:xi,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(gi(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function vi(e){const t=e._align;this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function bi(e){this.exit(e),this.data.inTable=void 0}function wi(e){this.enter({type:"tableRow",children:[]},e)}function Si(e){this.exit(e)}function Ci(e){this.enter({type:"tableCell",children:[]},e)}function Ei(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,Ii));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function Ii(e,t){return"|"===t?t:e}function Ti(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,o=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=ki.inlineCode(e,t,n);n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&"));return r},table:function(e,t,n,r){return s(function(e,t,n){const r=e.children;let o=-1;const i=[],l=t.enter("table");for(;++o<r.length;)i[o]=a(r[o],t,n);return l(),i}(e,n,r),e.align)},tableCell:l,tableRow:function(e,t,n,r){const o=s([a(e,n,r)]);return o.slice(0,o.indexOf("\n"))}}};function l(e,t,n,r){const o=n.enter("tableCell"),l=n.enter("phrasing"),s=n.containerPhrasing(e,{...r,before:i,after:i});return l(),o(),s}function s(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),o=n.stringLength||Jo,i=[],l=[],s=[],a=[];let c=0,u=-1;for(;++u<e.length;){const t=[],r=[];let i=-1;for(e[u].length>c&&(c=e[u].length);++i<e[u].length;){const l=Xo(e[u][i]);if(!1!==n.alignDelimiters){const e=o(l);r[i]=e,(void 0===a[i]||e>a[i])&&(a[i]=e)}t.push(l)}l[u]=t,s[u]=r}let f=-1;if("object"==typeof r&&"length"in r)for(;++f<c;)i[f]=Zo(r[f]);else{const e=Zo(r);for(;++f<c;)i[f]=e}f=-1;const d=[],p=[];for(;++f<c;){const e=i[f];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let o=!1===n.alignDelimiters?1:Math.max(1,a[f]-t.length-r.length);const l=t+"-".repeat(o)+r;!1!==n.alignDelimiters&&(o=t.length+o+r.length,o>a[f]&&(a[f]=o),p[f]=o),d[f]=l}l.splice(1,0,d),s.splice(1,0,p),u=-1;const h=[];for(;++u<l.length;){const e=l[u],t=s[u];f=-1;const r=[];for(;++f<c;){const o=e[f]||"";let l="",s="";if(!1!==n.alignDelimiters){const e=a[f]-(t[f]||0),n=i[f];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):(l=" ".repeat(e/2),s=l):s=" ".repeat(e)}!1===n.delimiterStart||f||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===o||!1===n.delimiterStart&&!f||r.push(" "),!1!==n.alignDelimiters&&r.push(l),r.push(o),!1!==n.alignDelimiters&&r.push(s),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&f===c-1||r.push("|")}h.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return h.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:o})}function a(e,t,n){const r=e.children;let o=-1;const i=[],s=t.enter("tableRow");for(;++o<r.length;)i[o]=l(r[o],0,t,n);return s(),i}}function Ai(e){const t=this.stack[this.stack.length-2];t.type,t.checked="taskListCheckValueChecked"===e.type}function Pi(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];e.type;const n=e.children[0];if(n&&"text"===n.type){const r=t.children;let o,i=-1;for(;++i<r.length;){const e=r[i];if("paragraph"===e.type){o=e;break}}o===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Li(e,t,n,r){const o=e.children[0],i="boolean"==typeof e.checked&&o&&"paragraph"===o.type,l="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);i&&s.move(l);let a=ki.listItem(e,t,n,{...r,...s.current()});return i&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+l})),a}const Di={tokenize:function(e,t,n){let r=0;return function t(i){if((87===i||119===i)&&r<3)return r++,e.consume(i),t;if(46===i&&3===r)return e.consume(i),o;return n(i)};function o(e){return null===e?n(e):t(e)}},partial:!0},ji={tokenize:function(e,t,n){let r,o,i;return l;function l(t){return 46===t||95===t?e.check(Ni,a,s)(t):null===t||Ct(t)||Tt(t)||45!==t&&It(t)?a(t):(i=!0,e.consume(t),l)}function s(t){return 95===t?r=!0:(o=r,r=void 0),e.consume(t),l}function a(e){return o||r||!i?n(e):t(e)}},partial:!0},Oi={tokenize:function(e,t){let n=0,r=0;return o;function o(l){return 40===l?(n++,e.consume(l),o):41===l&&r<n?i(l):33===l||34===l||38===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||60===l||63===l||93===l||95===l||126===l?e.check(Ni,t,i)(l):null===l||Ct(l)||Tt(l)?t(l):(e.consume(l),o)}function i(t){return 41===t&&r++,e.consume(t),o}},partial:!0},Ni={tokenize:function(e,t,n){return r;function r(l){return 33===l||34===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||63===l||95===l||126===l?(e.consume(l),r):38===l?(e.consume(l),i):93===l?(e.consume(l),o):60===l||null===l||Ct(l)||Tt(l)?t(l):n(l)}function o(e){return null===e||40===e||91===e||Ct(e)||Tt(e)?t(e):r(e)}function i(e){return gt(e)?l(e):n(e)}function l(t){return 59===t?(e.consume(t),r):gt(t)?(e.consume(t),l):n(t)}},partial:!0},Mi={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return yt(e)?n(e):t(e)}},partial:!0},Fi={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){if(87!==t&&119!==t||!Hi.call(r,r.previous)||Wi(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(Di,e.attempt(ji,e.attempt(Oi,o),n),n)(t)};function o(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:Hi},zi={name:"protocolAutolink",tokenize:function(e,t,n){const r=this;let o="",i=!1;return function(t){if((72===t||104===t)&&Ui.call(r,r.previous)&&!Wi(r.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),o+=String.fromCodePoint(t),e.consume(t),l;return n(t)};function l(t){if(gt(t)&&o.length<5)return o+=String.fromCodePoint(t),e.consume(t),l;if(58===t){const n=o.toLowerCase();if("http"===n||"https"===n)return e.consume(t),s}return n(t)}function s(t){return 47===t?(e.consume(t),i?a:(i=!0,s)):n(t)}function a(t){return null===t||kt(t)||Ct(t)||Tt(t)||It(t)?n(t):e.attempt(ji,e.attempt(Oi,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Ui},Ri={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let o,i;return function(t){if(!qi(t)||!Vi.call(r,r.previous)||Wi(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),l(t)};function l(t){return qi(t)?(e.consume(t),l):64===t?(e.consume(t),s):n(t)}function s(t){return 46===t?e.check(Mi,c,a)(t):45===t||95===t||yt(t)?(i=!0,e.consume(t),s):c(t)}function a(t){return e.consume(t),o=!0,s}function c(l){return i&&o&&gt(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):n(l)}},previous:Vi},_i={};let Bi=48;for(;Bi<123;)_i[Bi]=Ri,Bi++,58===Bi?Bi=65:91===Bi&&(Bi=97);function Hi(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||Ct(e)}function Ui(e){return!gt(e)}function Vi(e){return!(47===e||qi(e))}function qi(e){return 43===e||45===e||46===e||95===e||yt(e)}function Wi(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}_i[43]=Ri,_i[45]=Ri,_i[46]=Ri,_i[95]=Ri,_i[72]=[Ri,zi],_i[104]=[Ri,zi],_i[87]=[Ri,Fi],_i[119]=[Ri,Fi];const $i={tokenize:function(e,t,n){const r=this;return Lt(e,function(e){const o=r.events[r.events.length-1];return o&&"gfmFootnoteDefinitionIndent"===o[1].type&&4===o[2].sliceSerialize(o[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function Yi(e,t,n){const r=this;let o=r.events.length;const i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l;for(;o--;){const e=r.events[o][1];if("labelImage"===e.type){l=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!l||!l._balanced)return n(o);const s=mt(r.sliceSerialize({start:l.end,end:r.now()}));if(94!==s.codePointAt(0)||!i.includes(s.slice(1)))return n(o);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)}}function Ki(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},o={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};o.end.column++,o.end.offset++,o.end._bufferIndex++;const i={type:"gfmFootnoteCallString",start:Object.assign({},o.end),end:Object.assign({},e[e.length-1][1].start)},l={type:"chunkString",contentType:"string",start:Object.assign({},i.start),end:Object.assign({},i.end)},s=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",o,t],["exit",o,t],["enter",i,t],["enter",l,t],["exit",l,t],["exit",i,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...s),e}function Qi(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,l=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),s};function s(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",a)}function a(s){if(l>999||93===s&&!i||null===s||91===s||Ct(s))return n(s);if(93===s){e.exit("chunkString");const i=e.exit("gfmFootnoteCallString");return o.includes(mt(r.sliceSerialize(i)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(s),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(s)}return Ct(s)||(i=!0),l++,e.consume(s),92===s?c:a}function c(t){return 91===t||92===t||93===t?(e.consume(t),l++,a):a(t)}}function Ji(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,l,s=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),a};function a(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(s>999||93===t&&!l||null===t||91===t||Ct(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return i=mt(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),f}return Ct(t)||(l=!0),s++,e.consume(t),92===t?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}function f(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o.includes(i)||o.push(i),Lt(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function Xi(e,t,n){return e.check(_t,t,e.attempt($i,t,n))}function Zi(e){e.exit("gfmFootnoteDefinition")}function Gi(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const o=this.previous,i=this.events;let l=0;return function(t){if(126===o&&"characterEscape"!==i[i.length-1][1].type)return r(t);return e.enter("strikethroughSequenceTemporary"),s(t)};function s(i){const a=Nt(o);if(126===i)return l>1?r(i):(e.consume(i),l++,s);if(l<2&&!t)return r(i);const c=e.exit("strikethroughSequenceTemporary"),u=Nt(i);return c._open=!u||2===u&&Boolean(a),c._close=!a||2===a&&Boolean(u),n(i)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset===e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const o={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},l=[["enter",o,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",i,t]],s=t.parser.constructs.insideSpan.null;s&&at(l,l.length,0,Mt(s,e.slice(r+1,n),t)),at(l,l.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",o,t]]),at(e,r-1,n-r+3,l),n=r+l.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}class el{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let o=0;if(0===n&&0===r.length)return;for(;o<e.map.length;){if(e.map[o][0]===t)return e.map[o][1]+=n,void e.map[o][2].push(...r);o+=1}e.map.push([t,n,r])}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(const t of r)e.push(t);r=n.pop()}this.map.length=0}}function tl(e,t){let n=!1;const r=[];for(;t<e.length;){const o=e[t];if(n){if("enter"===o[0])"tableContent"===o[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===o[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===o[1].type)break}else"enter"===o[0]&&"tableDelimiterRow"===o[1].type&&(n=!0);t+=1}return r}function nl(e,t,n){const r=this;let o,i=0,l=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const o=t>-1?r.events[t][1].type:null,i="tableHead"===o||"tableRow"===o?v:s;if(i===v&&r.parser.lazy[r.now().line])return n(e);return i(e)};function s(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return a(e);return o=!0,l+=1,a(e)}(t)}function a(t){return null===t?n(t):St(t)?l>1?(l=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),f):n(t):Et(t)?Lt(e,a,"whitespace")(t):(l+=1,o&&(o=!1,i+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),o=!0,a):(e.enter("data"),c(t)))}function c(t){return null===t||124===t||Ct(t)?(e.exit("data"),a(t)):(e.consume(t),92===t?u:c)}function u(t){return 92===t||124===t?(e.consume(t),c):c(t)}function f(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),o=!1,Et(t)?Lt(e,d,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t))}function d(t){return 45===t||58===t?h(t):124===t?(o=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):k(t)}function p(t){return Et(t)?Lt(e,h,"whitespace")(t):h(t)}function h(t){return 58===t?(l+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),m):45===t?(l+=1,m(t)):null===t||St(t)?x(t):k(t)}function m(t){return 45===t?(e.enter("tableDelimiterFiller"),g(t)):k(t)}function g(t){return 45===t?(e.consume(t),g):58===t?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(t))}function y(t){return Et(t)?Lt(e,x,"whitespace")(t):x(t)}function x(n){return 124===n?d(n):(null===n||St(n))&&o&&i===l?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):k(n)}function k(e){return n(e)}function v(t){return e.enter("tableRow"),b(t)}function b(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),b):null===n||St(n)?(e.exit("tableRow"),t(n)):Et(n)?Lt(e,b,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||Ct(t)?(e.exit("data"),b(t)):(e.consume(t),92===t?S:w)}function S(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function rl(e,t){let n,r,o,i=-1,l=!0,s=0,a=[0,0,0,0],c=[0,0,0,0],u=!1,f=0;const d=new el;for(;++i<e.length;){const p=e[i],h=p[1];"enter"===p[0]?"tableHead"===h.type?(u=!1,0!==f&&(il(d,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(l=!0,o=void 0,a=[0,0,0,0],c=[0,i+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",r,t]])),s="tableDelimiterRow"===h.type?2:r?3:1):!s||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type?"tableCellDivider"===h.type&&(l?l=!1:(0!==a[1]&&(c[0]=c[1],o=ol(d,t,a,s,void 0,o)),a=c,c=[a[1],i,0,0])):(l=!1,0===c[2]&&(0!==a[1]&&(c[0]=c[1],o=ol(d,t,a,s,void 0,o),a=[0,0,0,0]),c[2]=i)):"tableHead"===h.type?(u=!0,f=i):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=i,0!==a[1]?(c[0]=c[1],o=ol(d,t,a,s,i,o)):0!==c[1]&&(o=ol(d,t,c,s,i,o)),s=0):!s||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type||(c[3]=i)}for(0!==f&&il(d,t,f,n,r),d.consume(t.events),i=-1;++i<t.events.length;){const e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=tl(t.events,i))}return e}function ol(e,t,n,r,o,i){const l=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(i.end=Object.assign({},ll(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));const s=ll(t.events,n[1]);if(i={type:l,start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){const o=ll(t.events,n[2]),i=ll(t.events,n[3]),l={type:"tableContent",start:Object.assign({},o),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",l,t]]),2!==r){const r=t.events[n[2]],o=t.events[n[3]];if(r[1].end=Object.assign({},o[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",l,t]])}return void 0!==o&&(i.end=Object.assign({},ll(t.events,o)),e.add(o,0,[["exit",i,t]]),i=void 0),i}function il(e,t,n,r,o){const i=[],l=ll(t.events,n);o&&(o.end=Object.assign({},l),i.push(["exit",o,t])),r.end=Object.assign({},l),i.push(["exit",r,t]),e.add(n+1,0,i)}function ll(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}const sl={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),o};function o(t){return Ct(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(t)}function l(r){return St(r)?t(r):Et(r)?e.check({tokenize:al},t,n)(r):n(r)}}};function al(e,t,n){return Lt(e,function(e){return null===e?n(e):t(e)},"whitespace")}const cl={};function ul(e){const t=e||cl,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),o=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),i=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(function(e){return ft([{text:_i},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Ji,continuation:{tokenize:Xi},exit:Zi}},text:{91:{name:"gfmFootnoteCall",tokenize:Qi},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:Yi,resolveTo:Ki}}},Gi(e),{flow:{null:{name:"table",tokenize:nl,resolveAll:rl}}},{text:{91:sl}}])}(t)),o.push([{transforms:[Lo],enter:{literalAutolink:Co,literalAutolinkEmail:Eo,literalAutolinkHttp:Eo,literalAutolinkWww:Eo},exit:{literalAutolink:Po,literalAutolinkEmail:Ao,literalAutolinkHttp:Io,literalAutolinkWww:To}},{enter:{gfmFootnoteCallString:No,gfmFootnoteCall:Mo,gfmFootnoteDefinitionLabelString:Fo,gfmFootnoteDefinition:zo},exit:{gfmFootnoteCallString:Ro,gfmFootnoteCall:_o,gfmFootnoteDefinitionLabelString:Bo,gfmFootnoteDefinition:Ho}},{canContainEols:["delete"],enter:{strikethrough:Yo},exit:{strikethrough:Ko}},{enter:{table:vi,tableData:Ci,tableHeader:Ci,tableRow:wi},exit:{codeText:Ei,table:bi,tableData:Si,tableHeader:Si,tableRow:Si}},{exit:{taskListCheckValueChecked:Ai,taskListCheckValueUnchecked:Ai,paragraph:Pi}}]),i.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:wo,notInConstruct:So},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:wo,notInConstruct:So},{character:":",before:"[ps]",after:"\\/",inConstruct:wo,notInConstruct:So}]},Vo(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:$o}],handlers:{delete:Qo}},Ti(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Li}}]}}(t))}const fl=({messagePair:e})=>{const{question:t,answer:n,timestamp:r,isAnswering:o=!1}=e;return h.jsxs("div",{className:"space-y-4 animate-slide-up message-enter",children:[t&&h.jsx("div",{className:"flex justify-end",children:h.jsxs("div",{className:"flex items-start space-x-3 max-w-xs lg:max-w-md flex-row-reverse space-x-reverse",children:[h.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center",children:h.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),h.jsx("div",{className:"flex flex-col",children:h.jsx("div",{className:"message-bubble user-message",children:h.jsx("p",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:t})})})]})}),h.jsx("div",{className:"flex justify-start",children:h.jsxs("div",{className:"flex items-start space-x-3 max-w-xs lg:max-w-md",children:[h.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:h.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),h.jsxs("div",{className:"flex flex-col",children:[h.jsx("div",{className:"message-bubble ai-message",children:h.jsx("div",{className:"text-sm leading-relaxed markdown-content",children:o&&!n?h.jsxs("div",{className:"flex space-x-1 py-2",children:[h.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),h.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),h.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}):h.jsx(go,{remarkPlugins:[ul],components:{p:({children:e})=>h.jsx("p",{className:"mb-2 last:mb-0",children:e}),h1:({children:e})=>h.jsx("h1",{className:"text-lg font-bold mb-2",children:e}),h2:({children:e})=>h.jsx("h2",{className:"text-base font-bold mb-2",children:e}),h3:({children:e})=>h.jsx("h3",{className:"text-sm font-bold mb-1",children:e}),ul:({children:e})=>h.jsx("ul",{className:"list-disc list-inside mb-2 space-y-1",children:e}),ol:({children:e})=>h.jsx("ol",{className:"list-decimal list-inside mb-2 space-y-1",children:e}),li:({children:e})=>h.jsx("li",{className:"text-sm",children:e}),blockquote:({children:e})=>h.jsx("blockquote",{className:"border-l-2 border-gray-300 pl-3 italic text-gray-600 mb-2",children:e}),code:({inline:e,children:t})=>e?h.jsx("code",{className:"bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono",children:t}):h.jsx("code",{className:"block bg-gray-100 text-gray-800 p-2 rounded text-xs font-mono overflow-x-auto mb-2",children:t}),pre:({children:e})=>h.jsx("pre",{className:"bg-gray-100 text-gray-800 p-3 rounded text-xs font-mono overflow-x-auto mb-2",children:e}),strong:({children:e})=>h.jsx("strong",{className:"font-semibold",children:e}),em:({children:e})=>h.jsx("em",{className:"italic",children:e}),a:({href:e,children:t})=>h.jsx("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:text-blue-700 underline",children:t}),table:({children:e})=>h.jsx("div",{className:"overflow-x-auto mb-2",children:h.jsx("table",{className:"min-w-full border border-gray-200 text-xs",children:e})}),thead:({children:e})=>h.jsx("thead",{className:"bg-gray-50",children:e}),th:({children:e})=>h.jsx("th",{className:"border border-gray-200 px-2 py-1 text-left font-semibold",children:e}),td:({children:e})=>h.jsx("td",{className:"border border-gray-200 px-2 py-1",children:e})},children:n||""})})}),h.jsx("div",{className:"text-xs text-gray-400 mt-1 text-left",children:(i=r,new Intl.DateTimeFormat("zh-CN",{hour:"2-digit",minute:"2-digit"}).format(i))})]})]})})]});var i},dl=({messagePairs:e,messagesEndRef:t})=>h.jsx("div",{className:"flex-1 overflow-y-auto custom-scrollbar",children:h.jsx("div",{className:"max-w-4xl mx-auto px-4 py-6",children:h.jsxs("div",{className:"space-y-6",children:[e.map((e,t)=>h.jsx(fl,{messagePair:e},t)),h.jsx("div",{ref:t})]})})}),pl=({onSendMessage:t,disabled:n=!1})=>{const[r,o]=e.useState(""),i=e.useRef(null),l=e=>{e.preventDefault(),r.trim()&&!n&&(t(r),o(""),i.current&&(i.current.style.height="auto"))};return h.jsx("div",{className:"bg-white border-t border-gray-200 px-4 py-4 shadow-lg",children:h.jsxs("div",{className:"max-w-4xl mx-auto",children:[h.jsxs("form",{onSubmit:l,className:"flex items-center space-x-4",children:[h.jsx("div",{className:"flex-1 chat-input-container",children:h.jsx("textarea",{ref:i,value:r,onChange:e=>{o(e.target.value);const t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),l(e))},placeholder:"输入你的消息... (Shift+Enter 换行)",className:"chat-input",disabled:n,rows:1,style:{minHeight:"48px",maxHeight:"120px"}})}),h.jsx("div",{className:"flex-shrink-0",children:h.jsxs("button",{type:"submit",disabled:!r.trim()||n,className:"send-button flex items-center justify-center space-x-2",style:{height:"48px",minWidth:"80px"},children:[n?h.jsx("svg",{className:"w-4 h-4 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):h.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})}),h.jsx("span",{children:n?"发送中...":"发送"})]})})]}),h.jsx("div",{className:"mt-2 text-xs text-gray-400 text-center",children:"按 Enter 发送消息，Shift+Enter 换行"})]})})};const hl="think-chat-message-pairs";function ml(e){try{localStorage.setItem(hl,JSON.stringify(e))}catch(t){}}function gl(){const[t,n]=e.useState(()=>{const e=function(){try{const e=localStorage.getItem(hl);return e?JSON.parse(e).map(e=>({...e,timestamp:new Date(e.timestamp)})):[]}catch(e){return[]}}();return e.length>0?e:[{answer:"你好！我是AI助手，有什么可以帮助你的吗？",timestamp:new Date}]}),[r,o]=e.useState(!1),i=e.useRef(null);e.useEffect(()=>{var e;null==(e=i.current)||e.scrollIntoView({behavior:"smooth"})},[t]),e.useEffect(()=>{ml(t)},[t]);return h.jsxs("div",{className:"flex flex-col h-screen bg-gray-50",children:[h.jsx(y,{onClearChat:()=>{const e={answer:"你好！我是AI助手，有什么可以帮助你的吗？",timestamp:new Date};n([e]),function(){try{localStorage.removeItem(hl)}catch(e){}}(),ml([e])}}),h.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[h.jsx(dl,{messagePairs:t,isLoading:r,messagesEndRef:i}),h.jsx(pl,{onSendMessage:async e=>{var r;if(!e.trim())return;o(!0);const i={question:e.trim(),answer:"",timestamp:new Date,isAnswering:!0};n(e=>[...e,i]);const l=null==(r=t[t.length-1])?void 0:r.id;let s=0;!async function(e,t,n,r,o){var i;try{const r=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json",Accept:"text/event-stream"},body:JSON.stringify({input:e,previous_id:o})});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const s=null==(i=r.body)?void 0:i.getReader();if(!s)throw new Error("无法获取响应流");const a=new TextDecoder;let c="";try{for(;;){const{done:e,value:r}=await s.read();if(e){n();break}c+=a.decode(r,{stream:!0});const o=c.split("\n");c=o.pop()||"";for(const i of o)if(""!==i.trim()&&i.startsWith("data: ")){const e=i.slice(6);if("[DONE]"===e)return void n();try{t(JSON.parse(e))}catch(l){t(e)}}}}finally{s.releaseLock()}}catch(s){r(s instanceof Error?s.message:"网络请求失败")}}(e.trim(),e=>{e.chunks?e.chunks.content&&n(t=>t.map((n,r)=>{if(r===t.length-1){let t=n.answer;const r=e.chunks.index;return r!==s&&(s=r,t+="\n\n"),t+=e.chunks.content,{...n,answer:t,isAnswering:!1}}return n})):e.stats||e.id&&n(t=>t.map((n,r)=>r===t.length-1?{...n,id:e.id}:n))},()=>{o(!1)},e=>{n(t=>t.map((n,r)=>r===t.length-1?{...n,answer:`❌ 错误: ${e}`,isAnswering:!1}:n)),o(!1)},l)},disabled:r})]})]})}m.createRoot(document.getElementById("root")).render(h.jsx(o.StrictMode,{children:h.jsx(gl,{})}));
